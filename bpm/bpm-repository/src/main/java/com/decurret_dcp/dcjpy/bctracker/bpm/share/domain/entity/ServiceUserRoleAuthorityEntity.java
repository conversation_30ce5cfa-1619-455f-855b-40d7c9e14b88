/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RoleId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceUserAuthorityKey;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスユーザロール権限 : 銀行 / 事業者ユーザのロールごとの操作権限を扱う。.
 */
@Entity(immutable = true)
@Table(name = "service_user_role_authority")
public class ServiceUserRoleAuthorityEntity {

    /** ロールID. */
    @Id
    @Column(name = "role_id")
    public final RoleId roleId;

    /** 権限名. */
    @Id
    @Column(name = "authority_key")
    public final ServiceUserAuthorityKey authorityKey;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param roleId ロールID
     * @param authorityKey 権限名
     */
    ServiceUserRoleAuthorityEntity(
        RoleId roleId,
        ServiceUserAuthorityKey authorityKey
    ) {
        this.roleId = roleId;
        this.authorityKey = authorityKey;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceUserRoleAuthorityEntity オブジェクト
     */
    protected ServiceUserRoleAuthorityEntity(ServiceUserRoleAuthorityEntity org) {
        this.roleId = org.roleId;
        this.authorityKey = org.authorityKey;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceUserRoleAuthorityEntity [")
                .append("roleId=").append(this.roleId).append(", ")
                .append("authorityKey=").append(this.authorityKey)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.roleId, 
                this.authorityKey
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceUserRoleAuthorityEntity other = (ServiceUserRoleAuthorityEntity) obj;
        return true
                && Objects.equals(this.roleId, other.roleId)
                && Objects.equals(this.authorityKey, other.authorityKey)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceUserRoleAuthorityEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** ロールID. */
        private RoleId roleId;

        /** 権限名. */
        private ServiceUserAuthorityKey authorityKey;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceUserRoleAuthorityEntity object
         */
        public ServiceUserRoleAuthorityEntity build() {
            return new ServiceUserRoleAuthorityEntity(
                    this.roleId, 
                    this.authorityKey
            );
        }

        /**
         * Set roleId.
         *
         * @param roleId ロールID
         * @return this builder
         */
        public Builder roleId(RoleId roleId) {
            this.roleId = roleId;
            return this;
        }

        /**
         * Set authorityKey.
         *
         * @param authorityKey 権限名
         * @return this builder
         */
        public Builder authorityKey(ServiceUserAuthorityKey authorityKey) {
            this.authorityKey = authorityKey;
            return this;
        }
    }
}
// @formatter:on
