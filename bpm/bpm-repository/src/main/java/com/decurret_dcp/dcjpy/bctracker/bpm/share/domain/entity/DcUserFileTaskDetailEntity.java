/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskDetailStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskOrderDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskResultDetail;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザファイルタスク詳細 : 個人 / 法人ユーザのファイルタスクの詳細を管理する。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_file_task_detail")
public class DcUserFileTaskDetailEntity {

    /** タスクID. */
    @Id
    @Column(name = "task_id")
    public final FileTaskId taskId;

    /** 行番号. */
    @Id
    @Column(name = "line_number")
    public final Integer lineNumber;

    /** タスク詳細状態. */
    @Column(name = "task_detail_status")
    public final FileTaskDetailStatus taskDetailStatus;

    /** タスク入力内容詳細. */
    @Column(name = "task_order_detail")
    public final FileTaskOrderDetail taskOrderDetail;

    /** 処理結果詳細. */
    @Column(name = "task_result_detail")
    public final FileTaskResultDetail taskResultDetail;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param taskId タスクID
     * @param lineNumber 行番号
     * @param taskDetailStatus タスク詳細状態
     * @param taskOrderDetail タスク入力内容詳細
     * @param taskResultDetail 処理結果詳細
     */
    DcUserFileTaskDetailEntity(
        FileTaskId taskId,
        Integer lineNumber,
        FileTaskDetailStatus taskDetailStatus,
        FileTaskOrderDetail taskOrderDetail,
        FileTaskResultDetail taskResultDetail
    ) {
        this.taskId = taskId;
        this.lineNumber = lineNumber;
        this.taskDetailStatus = taskDetailStatus;
        this.taskOrderDetail = taskOrderDetail;
        this.taskResultDetail = taskResultDetail;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserFileTaskDetailEntity オブジェクト
     */
    protected DcUserFileTaskDetailEntity(DcUserFileTaskDetailEntity org) {
        this.taskId = org.taskId;
        this.lineNumber = org.lineNumber;
        this.taskDetailStatus = org.taskDetailStatus;
        this.taskOrderDetail = org.taskOrderDetail;
        this.taskResultDetail = org.taskResultDetail;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserFileTaskDetailEntity [")
                .append("taskId=").append(this.taskId).append(", ")
                .append("lineNumber=").append(this.lineNumber).append(", ")
                .append("taskDetailStatus=").append(this.taskDetailStatus).append(", ")
                .append("taskOrderDetail=").append(this.taskOrderDetail).append(", ")
                .append("taskResultDetail=").append(this.taskResultDetail)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.taskId, 
                this.lineNumber, 
                this.taskDetailStatus, 
                this.taskOrderDetail, 
                this.taskResultDetail
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserFileTaskDetailEntity other = (DcUserFileTaskDetailEntity) obj;
        return true
                && Objects.equals(this.taskId, other.taskId)
                && Objects.equals(this.lineNumber, other.lineNumber)
                && Objects.equals(this.taskDetailStatus, other.taskDetailStatus)
                && Objects.equals(this.taskOrderDetail, other.taskOrderDetail)
                && Objects.equals(this.taskResultDetail, other.taskResultDetail)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserFileTaskDetailEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** タスクID. */
        private FileTaskId taskId;

        /** 行番号. */
        private Integer lineNumber;

        /** タスク詳細状態. */
        private FileTaskDetailStatus taskDetailStatus;

        /** タスク入力内容詳細. */
        private FileTaskOrderDetail taskOrderDetail;

        /** 処理結果詳細. */
        private FileTaskResultDetail taskResultDetail;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserFileTaskDetailEntity object
         */
        public DcUserFileTaskDetailEntity build() {
            return new DcUserFileTaskDetailEntity(
                    this.taskId, 
                    this.lineNumber, 
                    this.taskDetailStatus, 
                    this.taskOrderDetail, 
                    this.taskResultDetail
            );
        }

        /**
         * Set taskId.
         *
         * @param taskId タスクID
         * @return this builder
         */
        public Builder taskId(FileTaskId taskId) {
            this.taskId = taskId;
            return this;
        }

        /**
         * Set lineNumber.
         *
         * @param lineNumber 行番号
         * @return this builder
         */
        public Builder lineNumber(Integer lineNumber) {
            this.lineNumber = lineNumber;
            return this;
        }

        /**
         * Set taskDetailStatus.
         *
         * @param taskDetailStatus タスク詳細状態
         * @return this builder
         */
        public Builder taskDetailStatus(FileTaskDetailStatus taskDetailStatus) {
            this.taskDetailStatus = taskDetailStatus;
            return this;
        }

        /**
         * Set taskOrderDetail.
         *
         * @param taskOrderDetail タスク入力内容詳細
         * @return this builder
         */
        public Builder taskOrderDetail(FileTaskOrderDetail taskOrderDetail) {
            this.taskOrderDetail = taskOrderDetail;
            return this;
        }

        /**
         * Set taskResultDetail.
         *
         * @param taskResultDetail 処理結果詳細
         * @return this builder
         */
        public Builder taskResultDetail(FileTaskResultDetail taskResultDetail) {
            this.taskResultDetail = taskResultDetail;
            return this;
        }
    }
}
// @formatter:on
