package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcAccountEmailEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserEntity;

public interface DcAccountRepository {

    public DcUserEntity findAccountOwner(AccountId accountId, ValidatorId validatorId);

    public DcAccountEmailEntity findDcAccountEmail(AccountId accountId, ValidatorId validatorId);
}
