/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * バリデータアカウント.
 */
@Entity(immutable = true)
@Table(name = "validator_account")
public class ValidatorAccountEntity {

    /** バリデータID. */
    @Id
    @Column(name = "validator_id")
    public final ValidatorId validatorId;

    /** DC口座番号. */
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param validatorId バリデータID
     * @param dcBankNumber DC口座番号
     */
    ValidatorAccountEntity(
        ValidatorId validatorId,
        DcBankNumber dcBankNumber
    ) {
        this.validatorId = validatorId;
        this.dcBankNumber = dcBankNumber;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ValidatorAccountEntity オブジェクト
     */
    protected ValidatorAccountEntity(ValidatorAccountEntity org) {
        this.validatorId = org.validatorId;
        this.dcBankNumber = org.dcBankNumber;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ValidatorAccountEntity [")
                .append("validatorId=").append(this.validatorId).append(", ")
                .append("dcBankNumber=").append(this.dcBankNumber)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.validatorId, 
                this.dcBankNumber
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ValidatorAccountEntity other = (ValidatorAccountEntity) obj;
        return true
                && Objects.equals(this.validatorId, other.validatorId)
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ValidatorAccountEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** バリデータID. */
        private ValidatorId validatorId;

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ValidatorAccountEntity object
         */
        public ValidatorAccountEntity build() {
            return new ValidatorAccountEntity(
                    this.validatorId, 
                    this.dcBankNumber
            );
        }

        /**
         * Set validatorId.
         *
         * @param validatorId バリデータID
         * @return this builder
         */
        public Builder validatorId(ValidatorId validatorId) {
            this.validatorId = validatorId;
            return this;
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }
    }
}
// @formatter:on
