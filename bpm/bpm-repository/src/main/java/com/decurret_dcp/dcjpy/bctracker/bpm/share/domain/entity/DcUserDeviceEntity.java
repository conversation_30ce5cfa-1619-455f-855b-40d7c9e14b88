/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OsType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ利用端末 : 個人 / 法人ユーザのスマートフォン(iOS / Android)にPush通知を送信するために必要なデバイストークン(push_token)を管理する。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_device")
public class DcUserDeviceEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** プッシュ通知用トークン. */
    @Column(name = "push_token")
    public final String pushToken;

    /** OS種別 : ios : iOS
android : Android. */
    @Column(name = "os_type")
    public final OsType osType;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param pushToken プッシュ通知用トークン
     * @param osType OS種別 : ios : iOS
android : Android
     */
    DcUserDeviceEntity(
        SignInId signInId,
        String pushToken,
        OsType osType
    ) {
        this.signInId = signInId;
        this.pushToken = pushToken;
        this.osType = osType;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserDeviceEntity オブジェクト
     */
    protected DcUserDeviceEntity(DcUserDeviceEntity org) {
        this.signInId = org.signInId;
        this.pushToken = org.pushToken;
        this.osType = org.osType;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserDeviceEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("pushToken=").append(this.pushToken).append(", ")
                .append("osType=").append(this.osType)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.pushToken, 
                this.osType
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserDeviceEntity other = (DcUserDeviceEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.pushToken, other.pushToken)
                && Objects.equals(this.osType, other.osType)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserDeviceEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** プッシュ通知用トークン. */
        private String pushToken;

        /** OS種別 : ios : iOS
android : Android. */
        private OsType osType;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserDeviceEntity object
         */
        public DcUserDeviceEntity build() {
            return new DcUserDeviceEntity(
                    this.signInId, 
                    this.pushToken, 
                    this.osType
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set pushToken.
         *
         * @param pushToken プッシュ通知用トークン
         * @return this builder
         */
        public Builder pushToken(String pushToken) {
            this.pushToken = pushToken;
            return this;
        }

        /**
         * Set osType.
         *
         * @param osType OS種別 : ios : iOS
android : Android
         * @return this builder
         */
        public Builder osType(OsType osType) {
            this.osType = osType;
            return this;
        }
    }
}
// @formatter:on
