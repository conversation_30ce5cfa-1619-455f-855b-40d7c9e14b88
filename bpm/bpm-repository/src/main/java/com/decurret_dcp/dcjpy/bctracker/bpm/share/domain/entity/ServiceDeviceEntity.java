/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OsType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスデバイス : 認証アプリのPush通知用トークンの登録先を管理する.
 */
@Entity(immutable = true)
@Table(name = "service_device")
public class ServiceDeviceEntity {

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** OS種別 : ios : iOS
android : Android. */
    @Id
    @Column(name = "os_type")
    public final OsType osType;

    /** プラットフォームARN : 認証アプリプッシュ通知トークン用の登録先. */
    @Column(name = "platform_arn")
    public final String platformArn;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param osType OS種別 : ios : iOS
android : Android
     * @param platformArn プラットフォームARN : 認証アプリプッシュ通知トークン用の登録先
     */
    ServiceDeviceEntity(
        ServiceId serviceId,
        OsType osType,
        String platformArn
    ) {
        this.serviceId = serviceId;
        this.osType = osType;
        this.platformArn = platformArn;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceDeviceEntity オブジェクト
     */
    protected ServiceDeviceEntity(ServiceDeviceEntity org) {
        this.serviceId = org.serviceId;
        this.osType = org.osType;
        this.platformArn = org.platformArn;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceDeviceEntity [")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("osType=").append(this.osType).append(", ")
                .append("platformArn=").append(this.platformArn)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.serviceId, 
                this.osType, 
                this.platformArn
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceDeviceEntity other = (ServiceDeviceEntity) obj;
        return true
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.osType, other.osType)
                && Objects.equals(this.platformArn, other.platformArn)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceDeviceEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** OS種別 : ios : iOS
android : Android. */
        private OsType osType;

        /** プラットフォームARN : 認証アプリプッシュ通知トークン用の登録先. */
        private String platformArn;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceDeviceEntity object
         */
        public ServiceDeviceEntity build() {
            return new ServiceDeviceEntity(
                    this.serviceId, 
                    this.osType, 
                    this.platformArn
            );
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set osType.
         *
         * @param osType OS種別 : ios : iOS
android : Android
         * @return this builder
         */
        public Builder osType(OsType osType) {
            this.osType = osType;
            return this;
        }

        /**
         * Set platformArn.
         *
         * @param platformArn プラットフォームARN : 認証アプリプッシュ通知トークン用の登録先
         * @return this builder
         */
        public Builder platformArn(String platformArn) {
            this.platformArn = platformArn;
            return this;
        }
    }
}
// @formatter:on
