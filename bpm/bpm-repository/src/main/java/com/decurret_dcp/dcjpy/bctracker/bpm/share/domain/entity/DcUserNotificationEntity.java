/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ通知 : DCユーザの通知を管理するテーブル.
 */
@Entity(immutable = true)
@Table(name = "dc_user_notification")
public class DcUserNotificationEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 通知ID. */
    @Id
    @Column(name = "notification_id")
    public final NotificationId notificationId;

    /** 通知種別 : order : 依頼通知
order_completed : 依頼承認通知
order_rejected : 依頼否認通知
transfer : 移転完了通知
account_updated : アカウント情報変更通知
sigin_in : サインイン通知
synchronous : ビジネスゾーンアカウント開設
biz_terminating : ビジネスゾーンアカウント解約. */
    @Column(name = "notification_type")
    public final NotificationType notificationType;

    /** メッセージ本文. */
    @Column(name = "message")
    public final String message;

    /** 通知詳細 : 各通知タイプごとに固有の情報をjson形式で登録する. */
    @Column(name = "notification_detail")
    public final NotificationDetail notificationDetail;

    /** 通知日時. */
    @Column(name = "published_at")
    public final AppTimeStamp publishedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param notificationId 通知ID
     * @param notificationType 通知種別 : order : 依頼通知
order_completed : 依頼承認通知
order_rejected : 依頼否認通知
transfer : 移転完了通知
account_updated : アカウント情報変更通知
sigin_in : サインイン通知
synchronous : ビジネスゾーンアカウント開設
biz_terminating : ビジネスゾーンアカウント解約
     * @param message メッセージ本文
     * @param notificationDetail 通知詳細 : 各通知タイプごとに固有の情報をjson形式で登録する
     * @param publishedAt 通知日時
     */
    DcUserNotificationEntity(
        SignInId signInId,
        NotificationId notificationId,
        NotificationType notificationType,
        String message,
        NotificationDetail notificationDetail,
        AppTimeStamp publishedAt
    ) {
        this.signInId = signInId;
        this.notificationId = notificationId;
        this.notificationType = notificationType;
        this.message = message;
        this.notificationDetail = notificationDetail;
        this.publishedAt = publishedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserNotificationEntity オブジェクト
     */
    protected DcUserNotificationEntity(DcUserNotificationEntity org) {
        this.signInId = org.signInId;
        this.notificationId = org.notificationId;
        this.notificationType = org.notificationType;
        this.message = org.message;
        this.notificationDetail = org.notificationDetail;
        this.publishedAt = org.publishedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserNotificationEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("notificationId=").append(this.notificationId).append(", ")
                .append("notificationType=").append(this.notificationType).append(", ")
                .append("message=").append(this.message).append(", ")
                .append("notificationDetail=").append(this.notificationDetail).append(", ")
                .append("publishedAt=").append(this.publishedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.notificationId, 
                this.notificationType, 
                this.message, 
                this.notificationDetail, 
                this.publishedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserNotificationEntity other = (DcUserNotificationEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.notificationId, other.notificationId)
                && Objects.equals(this.notificationType, other.notificationType)
                && Objects.equals(this.message, other.message)
                && Objects.equals(this.notificationDetail, other.notificationDetail)
                && Objects.equals(this.publishedAt, other.publishedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserNotificationEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 通知ID. */
        private NotificationId notificationId;

        /** 通知種別 : order : 依頼通知
order_completed : 依頼承認通知
order_rejected : 依頼否認通知
transfer : 移転完了通知
account_updated : アカウント情報変更通知
sigin_in : サインイン通知
synchronous : ビジネスゾーンアカウント開設
biz_terminating : ビジネスゾーンアカウント解約. */
        private NotificationType notificationType;

        /** メッセージ本文. */
        private String message;

        /** 通知詳細 : 各通知タイプごとに固有の情報をjson形式で登録する. */
        private NotificationDetail notificationDetail;

        /** 通知日時. */
        private AppTimeStamp publishedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserNotificationEntity object
         */
        public DcUserNotificationEntity build() {
            return new DcUserNotificationEntity(
                    this.signInId, 
                    this.notificationId, 
                    this.notificationType, 
                    this.message, 
                    this.notificationDetail, 
                    this.publishedAt
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set notificationId.
         *
         * @param notificationId 通知ID
         * @return this builder
         */
        public Builder notificationId(NotificationId notificationId) {
            this.notificationId = notificationId;
            return this;
        }

        /**
         * Set notificationType.
         *
         * @param notificationType 通知種別 : order : 依頼通知
order_completed : 依頼承認通知
order_rejected : 依頼否認通知
transfer : 移転完了通知
account_updated : アカウント情報変更通知
sigin_in : サインイン通知
synchronous : ビジネスゾーンアカウント開設
biz_terminating : ビジネスゾーンアカウント解約
         * @return this builder
         */
        public Builder notificationType(NotificationType notificationType) {
            this.notificationType = notificationType;
            return this;
        }

        /**
         * Set message.
         *
         * @param message メッセージ本文
         * @return this builder
         */
        public Builder message(String message) {
            this.message = message;
            return this;
        }

        /**
         * Set notificationDetail.
         *
         * @param notificationDetail 通知詳細 : 各通知タイプごとに固有の情報をjson形式で登録する
         * @return this builder
         */
        public Builder notificationDetail(NotificationDetail notificationDetail) {
            this.notificationDetail = notificationDetail;
            return this;
        }

        /**
         * Set publishedAt.
         *
         * @param publishedAt 通知日時
         * @return this builder
         */
        public Builder publishedAt(AppTimeStamp publishedAt) {
            this.publishedAt = publishedAt;
            return this;
        }
    }
}
// @formatter:on
