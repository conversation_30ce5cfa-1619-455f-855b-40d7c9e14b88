package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.attribute;

import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.atomic.AttributeDetail;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = EmailAttributeDetail.Builder.class)
public class EmailAttributeDetail implements AttributeDetailContent {

    private static final TypeReference<EmailAttributeDetail> TYPE_REFERENCE = new TypeReference<>() {
    };

    @JsonProperty("email_address")
    public final String emailAddress;

    @Override
    public AttributeDetail toAttributeDetail() {
        return AttributeDetail.of(this);
    }

    public static EmailAttributeDetail of(AttributeDetail attributeDetail) {
        return attributeDetail.getContent(TYPE_REFERENCE);
    }
}
