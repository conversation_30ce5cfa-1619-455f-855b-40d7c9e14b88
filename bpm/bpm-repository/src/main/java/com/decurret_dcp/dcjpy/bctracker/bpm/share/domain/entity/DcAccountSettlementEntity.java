/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SettlementType;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DC口座精算設定 : アカウントの支払い精算に関する設定情報を扱う.
 */
@Entity(immutable = true)
@Table(name = "dc_account_settlement")
public class DcAccountSettlementEntity {

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** 精算条件 : immediate : 即時実行
monthly_scheduled : 月次指定日指定. */
    @Column(name = "settlement_type")
    public final SettlementType settlementType;

    /** 精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される). */
    @Column(name = "scheduled_day")
    public final Short scheduledDay;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param dcBankNumber DC口座番号
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param settlementType 精算条件 : immediate : 即時実行
monthly_scheduled : 月次指定日指定
     * @param scheduledDay 精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される)
     */
    DcAccountSettlementEntity(
        DcBankNumber dcBankNumber,
        ServiceId serviceId,
        SettlementType settlementType,
        Short scheduledDay
    ) {
        this.dcBankNumber = dcBankNumber;
        this.serviceId = serviceId;
        this.settlementType = settlementType;
        this.scheduledDay = scheduledDay;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcAccountSettlementEntity オブジェクト
     */
    protected DcAccountSettlementEntity(DcAccountSettlementEntity org) {
        this.dcBankNumber = org.dcBankNumber;
        this.serviceId = org.serviceId;
        this.settlementType = org.settlementType;
        this.scheduledDay = org.scheduledDay;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcAccountSettlementEntity [")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("settlementType=").append(this.settlementType).append(", ")
                .append("scheduledDay=").append(this.scheduledDay)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.dcBankNumber, 
                this.serviceId, 
                this.settlementType, 
                this.scheduledDay
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcAccountSettlementEntity other = (DcAccountSettlementEntity) obj;
        return true
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.settlementType, other.settlementType)
                && Objects.equals(this.scheduledDay, other.scheduledDay)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcAccountSettlementEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** 精算条件 : immediate : 即時実行
monthly_scheduled : 月次指定日指定. */
        private SettlementType settlementType;

        /** 精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される). */
        private Short scheduledDay;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcAccountSettlementEntity object
         */
        public DcAccountSettlementEntity build() {
            return new DcAccountSettlementEntity(
                    this.dcBankNumber, 
                    this.serviceId, 
                    this.settlementType, 
                    this.scheduledDay
            );
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set settlementType.
         *
         * @param settlementType 精算条件 : immediate : 即時実行
monthly_scheduled : 月次指定日指定
         * @return this builder
         */
        public Builder settlementType(SettlementType settlementType) {
            this.settlementType = settlementType;
            return this;
        }

        /**
         * Set scheduledDay.
         *
         * @param scheduledDay 精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される)
         * @return this builder
         */
        public Builder scheduledDay(Short scheduledDay) {
            this.scheduledDay = scheduledDay;
            return this;
        }
    }
}
// @formatter:on
