package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserNotificationEntity;

import org.seasar.doma.Dao;
import org.seasar.doma.Insert;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

@ConfigAutowireable
@Dao
public interface DcUserNotificationDao {

    @Select
    public boolean exists(NotificationId notificationId);

    @Insert(sqlFile = true)
    public Result<DcUserNotificationEntity> insert(DcUserNotificationEntity entity);
}
