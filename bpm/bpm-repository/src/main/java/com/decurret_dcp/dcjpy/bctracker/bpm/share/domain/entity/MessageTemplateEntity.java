/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * メッセージテンプレート : 理由コードのマスタ。.
 */
@Entity(immutable = true)
@Table(name = "message_template")
public class MessageTemplateEntity {

    /** テンプレートキー : 
. */
    @Id
    @Column(name = "template_key")
    public final TemplateKey templateKey;

    /** テンプレート本文. */
    @Column(name = "template_content")
    public final String templateContent;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param templateKey テンプレートキー : 

     * @param templateContent テンプレート本文
     */
    MessageTemplateEntity(
        TemplateKey templateKey,
        String templateContent
    ) {
        this.templateKey = templateKey;
        this.templateContent = templateContent;
    }

    /**
     * コンストラクタ。
     * 
     * @param org MessageTemplateEntity オブジェクト
     */
    protected MessageTemplateEntity(MessageTemplateEntity org) {
        this.templateKey = org.templateKey;
        this.templateContent = org.templateContent;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("MessageTemplateEntity [")
                .append("templateKey=").append(this.templateKey).append(", ")
                .append("templateContent=").append(this.templateContent)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.templateKey, 
                this.templateContent
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        MessageTemplateEntity other = (MessageTemplateEntity) obj;
        return true
                && Objects.equals(this.templateKey, other.templateKey)
                && Objects.equals(this.templateContent, other.templateContent)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * MessageTemplateEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** テンプレートキー : 
. */
        private TemplateKey templateKey;

        /** テンプレート本文. */
        private String templateContent;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the MessageTemplateEntity object
         */
        public MessageTemplateEntity build() {
            return new MessageTemplateEntity(
                    this.templateKey, 
                    this.templateContent
            );
        }

        /**
         * Set templateKey.
         *
         * @param templateKey テンプレートキー : 

         * @return this builder
         */
        public Builder templateKey(TemplateKey templateKey) {
            this.templateKey = templateKey;
            return this;
        }

        /**
         * Set templateContent.
         *
         * @param templateContent テンプレート本文
         * @return this builder
         */
        public Builder templateContent(String templateContent) {
            this.templateContent = templateContent;
            return this;
        }
    }
}
// @formatter:on
