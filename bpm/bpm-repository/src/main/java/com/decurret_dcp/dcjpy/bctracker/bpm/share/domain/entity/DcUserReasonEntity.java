/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OperationType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ理由 : 個人 / 法人ユーザの状態が変更された際の理由を扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_reason")
public class DcUserReasonEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 操作種別. */
    @Column(name = "operation_type")
    public final OperationType operationType;

    /** 理由コード. */
    @Column(name = "reason_code")
    public final String reasonCode;

    /** 理由詳細. */
    @Column(name = "reason_detail")
    public final String reasonDetail;

    /** 操作日時. */
    @Column(name = "operated_at")
    public final AppTimeStamp operatedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param operationType 操作種別
     * @param reasonCode 理由コード
     * @param reasonDetail 理由詳細
     * @param operatedAt 操作日時
     */
    DcUserReasonEntity(
        SignInId signInId,
        OperationType operationType,
        String reasonCode,
        String reasonDetail,
        AppTimeStamp operatedAt
    ) {
        this.signInId = signInId;
        this.operationType = operationType;
        this.reasonCode = reasonCode;
        this.reasonDetail = reasonDetail;
        this.operatedAt = operatedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserReasonEntity オブジェクト
     */
    protected DcUserReasonEntity(DcUserReasonEntity org) {
        this.signInId = org.signInId;
        this.operationType = org.operationType;
        this.reasonCode = org.reasonCode;
        this.reasonDetail = org.reasonDetail;
        this.operatedAt = org.operatedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserReasonEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("operationType=").append(this.operationType).append(", ")
                .append("reasonCode=").append(this.reasonCode).append(", ")
                .append("reasonDetail=").append(this.reasonDetail).append(", ")
                .append("operatedAt=").append(this.operatedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.operationType, 
                this.reasonCode, 
                this.reasonDetail, 
                this.operatedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserReasonEntity other = (DcUserReasonEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.operationType, other.operationType)
                && Objects.equals(this.reasonCode, other.reasonCode)
                && Objects.equals(this.reasonDetail, other.reasonDetail)
                && Objects.equals(this.operatedAt, other.operatedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserReasonEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 操作種別. */
        private OperationType operationType;

        /** 理由コード. */
        private String reasonCode;

        /** 理由詳細. */
        private String reasonDetail;

        /** 操作日時. */
        private AppTimeStamp operatedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserReasonEntity object
         */
        public DcUserReasonEntity build() {
            return new DcUserReasonEntity(
                    this.signInId, 
                    this.operationType, 
                    this.reasonCode, 
                    this.reasonDetail, 
                    this.operatedAt
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set operationType.
         *
         * @param operationType 操作種別
         * @return this builder
         */
        public Builder operationType(OperationType operationType) {
            this.operationType = operationType;
            return this;
        }

        /**
         * Set reasonCode.
         *
         * @param reasonCode 理由コード
         * @return this builder
         */
        public Builder reasonCode(String reasonCode) {
            this.reasonCode = reasonCode;
            return this;
        }

        /**
         * Set reasonDetail.
         *
         * @param reasonDetail 理由詳細
         * @return this builder
         */
        public Builder reasonDetail(String reasonDetail) {
            this.reasonDetail = reasonDetail;
            return this;
        }

        /**
         * Set operatedAt.
         *
         * @param operatedAt 操作日時
         * @return this builder
         */
        public Builder operatedAt(AppTimeStamp operatedAt) {
            this.operatedAt = operatedAt;
            return this;
        }
    }
}
// @formatter:on
