/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザサインイン履歴 : 個人/法人ユーザの最新のサインイン日時と前回のサインイン日時を扱う。
ユーザがサインインすると、以下のように本テーブルのレコードを更新する。
- ユーザがサインインした日時が「最新のサインイン日時」に保存される
- 上記にあわせ、更新前の「最新のサインイン日時」の値が「前回のサインイン日時」に保存される

上記により、ユーザ自身が前回のサインイン日時を照会する場合は「前回のサインイン日時」列を参照し、ユーザ管理者がユーザ一一覧にて直近のサインイン日時を紹介する場合は「最新のサインイン日時」列を参照すればよい。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_sign_in_history")
public class DcUserSignInHistoryEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 最新のサインイン日時. */
    @Column(name = "current_signed_in_at")
    public final AppTimeStamp currentSignedInAt;

    /** 前回のサインイン日時. */
    @Column(name = "last_signed_in_at")
    public final AppTimeStamp lastSignedInAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param currentSignedInAt 最新のサインイン日時
     * @param lastSignedInAt 前回のサインイン日時
     */
    DcUserSignInHistoryEntity(
        SignInId signInId,
        AppTimeStamp currentSignedInAt,
        AppTimeStamp lastSignedInAt
    ) {
        this.signInId = signInId;
        this.currentSignedInAt = currentSignedInAt;
        this.lastSignedInAt = lastSignedInAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserSignInHistoryEntity オブジェクト
     */
    protected DcUserSignInHistoryEntity(DcUserSignInHistoryEntity org) {
        this.signInId = org.signInId;
        this.currentSignedInAt = org.currentSignedInAt;
        this.lastSignedInAt = org.lastSignedInAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserSignInHistoryEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("currentSignedInAt=").append(this.currentSignedInAt).append(", ")
                .append("lastSignedInAt=").append(this.lastSignedInAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.currentSignedInAt, 
                this.lastSignedInAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserSignInHistoryEntity other = (DcUserSignInHistoryEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.currentSignedInAt, other.currentSignedInAt)
                && Objects.equals(this.lastSignedInAt, other.lastSignedInAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserSignInHistoryEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 最新のサインイン日時. */
        private AppTimeStamp currentSignedInAt;

        /** 前回のサインイン日時. */
        private AppTimeStamp lastSignedInAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserSignInHistoryEntity object
         */
        public DcUserSignInHistoryEntity build() {
            return new DcUserSignInHistoryEntity(
                    this.signInId, 
                    this.currentSignedInAt, 
                    this.lastSignedInAt
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set currentSignedInAt.
         *
         * @param currentSignedInAt 最新のサインイン日時
         * @return this builder
         */
        public Builder currentSignedInAt(AppTimeStamp currentSignedInAt) {
            this.currentSignedInAt = currentSignedInAt;
            return this;
        }

        /**
         * Set lastSignedInAt.
         *
         * @param lastSignedInAt 前回のサインイン日時
         * @return this builder
         */
        public Builder lastSignedInAt(AppTimeStamp lastSignedInAt) {
            this.lastSignedInAt = lastSignedInAt;
            return this;
        }
    }
}
// @formatter:on
