/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.InformationId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * インフォメーション : インフォメーションを扱う。.
 */
@Entity(immutable = true)
@Table(name = "information")
public class InformationEntity {

    /** インフォメーションID : 
. */
    @Id
    @Column(name = "information_id")
    public final InformationId informationId;

    /** インフォメーションタイトル. */
    @Column(name = "information_title")
    public final String informationTitle;

    /** インフォメーション詳細. */
    @Column(name = "information_detail")
    public final String informationDetail;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** 公開開始日時. */
    @Column(name = "from_published_at")
    public final AppTimeStamp fromPublishedAt;

    /** 公開終了日時. */
    @Column(name = "to_published_at")
    public final AppTimeStamp toPublishedAt;

    /** 作成日時. */
    @Column(name = "created_at")
    public final AppTimeStamp createdAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param informationId インフォメーションID : 

     * @param informationTitle インフォメーションタイトル
     * @param informationDetail インフォメーション詳細
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param fromPublishedAt 公開開始日時
     * @param toPublishedAt 公開終了日時
     * @param createdAt 作成日時
     */
    InformationEntity(
        InformationId informationId,
        String informationTitle,
        String informationDetail,
        ServiceId serviceId,
        AppTimeStamp fromPublishedAt,
        AppTimeStamp toPublishedAt,
        AppTimeStamp createdAt
    ) {
        this.informationId = informationId;
        this.informationTitle = informationTitle;
        this.informationDetail = informationDetail;
        this.serviceId = serviceId;
        this.fromPublishedAt = fromPublishedAt;
        this.toPublishedAt = toPublishedAt;
        this.createdAt = createdAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org InformationEntity オブジェクト
     */
    protected InformationEntity(InformationEntity org) {
        this.informationId = org.informationId;
        this.informationTitle = org.informationTitle;
        this.informationDetail = org.informationDetail;
        this.serviceId = org.serviceId;
        this.fromPublishedAt = org.fromPublishedAt;
        this.toPublishedAt = org.toPublishedAt;
        this.createdAt = org.createdAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("InformationEntity [")
                .append("informationId=").append(this.informationId).append(", ")
                .append("informationTitle=").append(this.informationTitle).append(", ")
                .append("informationDetail=").append(this.informationDetail).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("fromPublishedAt=").append(this.fromPublishedAt).append(", ")
                .append("toPublishedAt=").append(this.toPublishedAt).append(", ")
                .append("createdAt=").append(this.createdAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.informationId, 
                this.informationTitle, 
                this.informationDetail, 
                this.serviceId, 
                this.fromPublishedAt, 
                this.toPublishedAt, 
                this.createdAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        InformationEntity other = (InformationEntity) obj;
        return true
                && Objects.equals(this.informationId, other.informationId)
                && Objects.equals(this.informationTitle, other.informationTitle)
                && Objects.equals(this.informationDetail, other.informationDetail)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.fromPublishedAt, other.fromPublishedAt)
                && Objects.equals(this.toPublishedAt, other.toPublishedAt)
                && Objects.equals(this.createdAt, other.createdAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * InformationEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** インフォメーションID : 
. */
        private InformationId informationId;

        /** インフォメーションタイトル. */
        private String informationTitle;

        /** インフォメーション詳細. */
        private String informationDetail;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** 公開開始日時. */
        private AppTimeStamp fromPublishedAt;

        /** 公開終了日時. */
        private AppTimeStamp toPublishedAt;

        /** 作成日時. */
        private AppTimeStamp createdAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the InformationEntity object
         */
        public InformationEntity build() {
            return new InformationEntity(
                    this.informationId, 
                    this.informationTitle, 
                    this.informationDetail, 
                    this.serviceId, 
                    this.fromPublishedAt, 
                    this.toPublishedAt, 
                    this.createdAt
            );
        }

        /**
         * Set informationId.
         *
         * @param informationId インフォメーションID : 

         * @return this builder
         */
        public Builder informationId(InformationId informationId) {
            this.informationId = informationId;
            return this;
        }

        /**
         * Set informationTitle.
         *
         * @param informationTitle インフォメーションタイトル
         * @return this builder
         */
        public Builder informationTitle(String informationTitle) {
            this.informationTitle = informationTitle;
            return this;
        }

        /**
         * Set informationDetail.
         *
         * @param informationDetail インフォメーション詳細
         * @return this builder
         */
        public Builder informationDetail(String informationDetail) {
            this.informationDetail = informationDetail;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set fromPublishedAt.
         *
         * @param fromPublishedAt 公開開始日時
         * @return this builder
         */
        public Builder fromPublishedAt(AppTimeStamp fromPublishedAt) {
            this.fromPublishedAt = fromPublishedAt;
            return this;
        }

        /**
         * Set toPublishedAt.
         *
         * @param toPublishedAt 公開終了日時
         * @return this builder
         */
        public Builder toPublishedAt(AppTimeStamp toPublishedAt) {
            this.toPublishedAt = toPublishedAt;
            return this;
        }

        /**
         * Set createdAt.
         *
         * @param createdAt 作成日時
         * @return this builder
         */
        public Builder createdAt(AppTimeStamp createdAt) {
            this.createdAt = createdAt;
            return this;
        }
    }
}
// @formatter:on
