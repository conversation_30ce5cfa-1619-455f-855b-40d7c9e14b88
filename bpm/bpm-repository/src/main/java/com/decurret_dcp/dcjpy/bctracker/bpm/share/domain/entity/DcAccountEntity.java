/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DC口座 : DC口座を扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_account")
public class DcAccountEntity {

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** アカウントID. */
    @Column(name = "account_id")
    public final AccountId accountId;

    /** Coreモジュール接続状態 : Coreモジュールにアカウントを作成済みかを示す. */
    @Column(name = "core_linked")
    public final Boolean coreLinked;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param dcBankNumber DC口座番号
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param accountId アカウントID
     * @param coreLinked Coreモジュール接続状態 : Coreモジュールにアカウントを作成済みかを示す
     */
    DcAccountEntity(
        DcBankNumber dcBankNumber,
        ServiceId serviceId,
        AccountId accountId,
        Boolean coreLinked
    ) {
        this.dcBankNumber = dcBankNumber;
        this.serviceId = serviceId;
        this.accountId = accountId;
        this.coreLinked = coreLinked;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcAccountEntity オブジェクト
     */
    protected DcAccountEntity(DcAccountEntity org) {
        this.dcBankNumber = org.dcBankNumber;
        this.serviceId = org.serviceId;
        this.accountId = org.accountId;
        this.coreLinked = org.coreLinked;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcAccountEntity [")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("accountId=").append(this.accountId).append(", ")
                .append("coreLinked=").append(this.coreLinked)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.dcBankNumber, 
                this.serviceId, 
                this.accountId, 
                this.coreLinked
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcAccountEntity other = (DcAccountEntity) obj;
        return true
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.accountId, other.accountId)
                && Objects.equals(this.coreLinked, other.coreLinked)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcAccountEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** アカウントID. */
        private AccountId accountId;

        /** Coreモジュール接続状態 : Coreモジュールにアカウントを作成済みかを示す. */
        private Boolean coreLinked;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcAccountEntity object
         */
        public DcAccountEntity build() {
            return new DcAccountEntity(
                    this.dcBankNumber, 
                    this.serviceId, 
                    this.accountId, 
                    this.coreLinked
            );
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set accountId.
         *
         * @param accountId アカウントID
         * @return this builder
         */
        public Builder accountId(AccountId accountId) {
            this.accountId = accountId;
            return this;
        }

        /**
         * Set coreLinked.
         *
         * @param coreLinked Coreモジュール接続状態 : Coreモジュールにアカウントを作成済みかを示す
         * @return this builder
         */
        public Builder coreLinked(Boolean coreLinked) {
            this.coreLinked = coreLinked;
            return this;
        }
    }
}
// @formatter:on
