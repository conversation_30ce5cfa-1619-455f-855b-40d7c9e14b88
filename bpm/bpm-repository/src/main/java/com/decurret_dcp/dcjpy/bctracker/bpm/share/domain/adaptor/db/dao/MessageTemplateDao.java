package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.MessageTemplateEntity;

@ConfigAutowireable
@Dao
public interface MessageTemplateDao {

    @Select
    public MessageTemplateEntity selectById(TemplateKey templateKey);
}
