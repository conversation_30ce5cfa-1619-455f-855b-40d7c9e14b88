package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.atomic;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

class JsonConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private JsonConverter() {
        // Do nothing.
    }

    static <TYPE> TYPE toJsonValue(String jsonString, TypeReference<TYPE> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(jsonString, typeReference);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException(jsonExc);
        }
    }

    static JsonNode toJsonNode(String jsonString) {
        try {
            return OBJECT_MAPPER.readTree(jsonString);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException(jsonExc);
        }
    }

    static String toStringValue(Object jsonValue) {
        try {
            return OBJECT_MAPPER.writeValueAsString(jsonValue);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException(jsonExc);
        }
    }
}
