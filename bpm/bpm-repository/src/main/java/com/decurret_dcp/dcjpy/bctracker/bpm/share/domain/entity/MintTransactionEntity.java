/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.MintStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * 発行履歴 : DCJPY 発行 / 償却の処理の状態を管理する。.
 */
@Entity(immutable = true)
@Table(name = "mint_transaction")
public class MintTransactionEntity {

    /** リクエストID. */
    @Id
    @Column(name = "request_id")
    public final RequestId requestId;

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** mint取引状態 : initialize : 処理開始前
withdrawn : 銀行預金口座から出金済み
withdraw_failed : 銀行預金口座から出金できなかった
minting_failed : 銀行預金口座から出金後、DCJPY 発行が失敗
completed : DCJPY 発行/償却手続きが正常に完了. */
    @Column(name = "mint_status")
    public final MintStatus mintStatus;

    /** 金額. */
    @Column(name = "amount")
    public final Amount amount;

    /** 初期登録日時. */
    @Column(name = "initialized_at")
    public final AppTimeStamp initializedAt;

    /** BankGW処理結果日時. */
    @Column(name = "bankgw_resulted_at")
    public final AppTimeStamp bankgwResultedAt;

    /** CoreAPI処理結果日時. */
    @Column(name = "coreapi_resulted_at")
    public final AppTimeStamp coreapiResultedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param requestId リクエストID
     * @param dcBankNumber DC口座番号
     * @param mintStatus mint取引状態 : initialize : 処理開始前
withdrawn : 銀行預金口座から出金済み
withdraw_failed : 銀行預金口座から出金できなかった
minting_failed : 銀行預金口座から出金後、DCJPY 発行が失敗
completed : DCJPY 発行/償却手続きが正常に完了
     * @param amount 金額
     * @param initializedAt 初期登録日時
     * @param bankgwResultedAt BankGW処理結果日時
     * @param coreapiResultedAt CoreAPI処理結果日時
     */
    MintTransactionEntity(
        RequestId requestId,
        DcBankNumber dcBankNumber,
        MintStatus mintStatus,
        Amount amount,
        AppTimeStamp initializedAt,
        AppTimeStamp bankgwResultedAt,
        AppTimeStamp coreapiResultedAt
    ) {
        this.requestId = requestId;
        this.dcBankNumber = dcBankNumber;
        this.mintStatus = mintStatus;
        this.amount = amount;
        this.initializedAt = initializedAt;
        this.bankgwResultedAt = bankgwResultedAt;
        this.coreapiResultedAt = coreapiResultedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org MintTransactionEntity オブジェクト
     */
    protected MintTransactionEntity(MintTransactionEntity org) {
        this.requestId = org.requestId;
        this.dcBankNumber = org.dcBankNumber;
        this.mintStatus = org.mintStatus;
        this.amount = org.amount;
        this.initializedAt = org.initializedAt;
        this.bankgwResultedAt = org.bankgwResultedAt;
        this.coreapiResultedAt = org.coreapiResultedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("MintTransactionEntity [")
                .append("requestId=").append(this.requestId).append(", ")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("mintStatus=").append(this.mintStatus).append(", ")
                .append("amount=").append(this.amount).append(", ")
                .append("initializedAt=").append(this.initializedAt).append(", ")
                .append("bankgwResultedAt=").append(this.bankgwResultedAt).append(", ")
                .append("coreapiResultedAt=").append(this.coreapiResultedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.requestId, 
                this.dcBankNumber, 
                this.mintStatus, 
                this.amount, 
                this.initializedAt, 
                this.bankgwResultedAt, 
                this.coreapiResultedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        MintTransactionEntity other = (MintTransactionEntity) obj;
        return true
                && Objects.equals(this.requestId, other.requestId)
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.mintStatus, other.mintStatus)
                && Objects.equals(this.amount, other.amount)
                && Objects.equals(this.initializedAt, other.initializedAt)
                && Objects.equals(this.bankgwResultedAt, other.bankgwResultedAt)
                && Objects.equals(this.coreapiResultedAt, other.coreapiResultedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * MintTransactionEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** リクエストID. */
        private RequestId requestId;

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** mint取引状態 : initialize : 処理開始前
withdrawn : 銀行預金口座から出金済み
withdraw_failed : 銀行預金口座から出金できなかった
minting_failed : 銀行預金口座から出金後、DCJPY 発行が失敗
completed : DCJPY 発行/償却手続きが正常に完了. */
        private MintStatus mintStatus;

        /** 金額. */
        private Amount amount;

        /** 初期登録日時. */
        private AppTimeStamp initializedAt;

        /** BankGW処理結果日時. */
        private AppTimeStamp bankgwResultedAt;

        /** CoreAPI処理結果日時. */
        private AppTimeStamp coreapiResultedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the MintTransactionEntity object
         */
        public MintTransactionEntity build() {
            return new MintTransactionEntity(
                    this.requestId, 
                    this.dcBankNumber, 
                    this.mintStatus, 
                    this.amount, 
                    this.initializedAt, 
                    this.bankgwResultedAt, 
                    this.coreapiResultedAt
            );
        }

        /**
         * Set requestId.
         *
         * @param requestId リクエストID
         * @return this builder
         */
        public Builder requestId(RequestId requestId) {
            this.requestId = requestId;
            return this;
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set mintStatus.
         *
         * @param mintStatus mint取引状態 : initialize : 処理開始前
withdrawn : 銀行預金口座から出金済み
withdraw_failed : 銀行預金口座から出金できなかった
minting_failed : 銀行預金口座から出金後、DCJPY 発行が失敗
completed : DCJPY 発行/償却手続きが正常に完了
         * @return this builder
         */
        public Builder mintStatus(MintStatus mintStatus) {
            this.mintStatus = mintStatus;
            return this;
        }

        /**
         * Set amount.
         *
         * @param amount 金額
         * @return this builder
         */
        public Builder amount(Amount amount) {
            this.amount = amount;
            return this;
        }

        /**
         * Set initializedAt.
         *
         * @param initializedAt 初期登録日時
         * @return this builder
         */
        public Builder initializedAt(AppTimeStamp initializedAt) {
            this.initializedAt = initializedAt;
            return this;
        }

        /**
         * Set bankgwResultedAt.
         *
         * @param bankgwResultedAt BankGW処理結果日時
         * @return this builder
         */
        public Builder bankgwResultedAt(AppTimeStamp bankgwResultedAt) {
            this.bankgwResultedAt = bankgwResultedAt;
            return this;
        }

        /**
         * Set coreapiResultedAt.
         *
         * @param coreapiResultedAt CoreAPI処理結果日時
         * @return this builder
         */
        public Builder coreapiResultedAt(AppTimeStamp coreapiResultedAt) {
            this.coreapiResultedAt = coreapiResultedAt;
            return this;
        }
    }
}
// @formatter:on
