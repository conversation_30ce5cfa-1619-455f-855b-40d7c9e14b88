package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao.DcAccountDao;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao.DcUserDao;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcAccountEmailEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.repository.DcAccountRepository;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

@Slf4j
@AllArgsConstructor
@Repository
public class DomaDcAccountRepository implements DcAccountRepository {

    private final DcAccountDao dcAccountDao;

    private final DcUserDao dcUserDao;

    @Override
    public DcUserEntity findAccountOwner(AccountId accountId, ValidatorId validatorId) {
        return this.dcUserDao.selectAccountOwner(accountId, validatorId);
    }

    @Override
    public DcAccountEmailEntity findDcAccountEmail(AccountId accountId, ValidatorId validatorId) {
        // ひとつの zone に対して、account_id が重複することはない制約があるので、
        // account_id と zone_id が指定されることで DC口座に関する情報はひとつに限定される。
        return this.dcAccountDao.selectEmailAddressByAccountId(accountId, validatorId);
    }
}
