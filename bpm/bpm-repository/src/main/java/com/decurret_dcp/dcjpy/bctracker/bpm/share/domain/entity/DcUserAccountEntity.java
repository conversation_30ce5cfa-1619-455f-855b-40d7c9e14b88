/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザアカウント : 個人 / 法人ユーザが紐づくDC口座番号を扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_account")
public class DcUserAccountEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** DC口座番号. */
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param dcBankNumber DC口座番号
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     */
    DcUserAccountEntity(
        SignInId signInId,
        DcBankNumber dcBankNumber,
        ServiceId serviceId
    ) {
        this.signInId = signInId;
        this.dcBankNumber = dcBankNumber;
        this.serviceId = serviceId;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserAccountEntity オブジェクト
     */
    protected DcUserAccountEntity(DcUserAccountEntity org) {
        this.signInId = org.signInId;
        this.dcBankNumber = org.dcBankNumber;
        this.serviceId = org.serviceId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserAccountEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("serviceId=").append(this.serviceId)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.dcBankNumber, 
                this.serviceId
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserAccountEntity other = (DcUserAccountEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.serviceId, other.serviceId)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserAccountEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserAccountEntity object
         */
        public DcUserAccountEntity build() {
            return new DcUserAccountEntity(
                    this.signInId, 
                    this.dcBankNumber, 
                    this.serviceId
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }
    }
}
// @formatter:on
