/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AdminId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスアドミン.
 */
@Entity(immutable = true)
@Table(name = "service_admin")
public class ServiceAdminEntity {

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** アドミンID. */
    @Column(name = "admin_id")
    public final AdminId adminId;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param adminId アドミンID
     */
    ServiceAdminEntity(
        ServiceId serviceId,
        AdminId adminId
    ) {
        this.serviceId = serviceId;
        this.adminId = adminId;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceAdminEntity オブジェクト
     */
    protected ServiceAdminEntity(ServiceAdminEntity org) {
        this.serviceId = org.serviceId;
        this.adminId = org.adminId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceAdminEntity [")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("adminId=").append(this.adminId)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.serviceId, 
                this.adminId
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceAdminEntity other = (ServiceAdminEntity) obj;
        return true
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.adminId, other.adminId)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceAdminEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** アドミンID. */
        private AdminId adminId;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceAdminEntity object
         */
        public ServiceAdminEntity build() {
            return new ServiceAdminEntity(
                    this.serviceId, 
                    this.adminId
            );
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set adminId.
         *
         * @param adminId アドミンID
         * @return this builder
         */
        public Builder adminId(AdminId adminId) {
            this.adminId = adminId;
            return this;
        }
    }
}
// @formatter:on
