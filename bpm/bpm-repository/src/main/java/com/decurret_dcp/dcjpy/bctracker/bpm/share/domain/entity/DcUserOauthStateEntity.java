/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OAuthState;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザOAuth状態 : 個人 / 法人ユーザの外部システムと OAuth で連携する際の state を管理する。
FinZone の場合 : BankGateway との認可で利用する
BizZone の場合 : FinZone Signer との認証で利用する.
 */
@Entity(immutable = true)
@Table(name = "dc_user_oauth_state")
public class DcUserOauthStateEntity {

    /** OAuth状態コード. */
    @Id
    @Column(name = "oauth_state")
    public final OAuthState oauthState;

    /** サインインID. */
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 有効期限. */
    @Column(name = "expires_at")
    public final AppTimeStamp expiresAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param oauthState OAuth状態コード
     * @param signInId サインインID
     * @param expiresAt 有効期限
     */
    DcUserOauthStateEntity(
        OAuthState oauthState,
        SignInId signInId,
        AppTimeStamp expiresAt
    ) {
        this.oauthState = oauthState;
        this.signInId = signInId;
        this.expiresAt = expiresAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserOauthStateEntity オブジェクト
     */
    protected DcUserOauthStateEntity(DcUserOauthStateEntity org) {
        this.oauthState = org.oauthState;
        this.signInId = org.signInId;
        this.expiresAt = org.expiresAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserOauthStateEntity [")
                .append("oauthState=").append(this.oauthState).append(", ")
                .append("signInId=").append(this.signInId).append(", ")
                .append("expiresAt=").append(this.expiresAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.oauthState, 
                this.signInId, 
                this.expiresAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserOauthStateEntity other = (DcUserOauthStateEntity) obj;
        return true
                && Objects.equals(this.oauthState, other.oauthState)
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.expiresAt, other.expiresAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserOauthStateEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** OAuth状態コード. */
        private OAuthState oauthState;

        /** サインインID. */
        private SignInId signInId;

        /** 有効期限. */
        private AppTimeStamp expiresAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserOauthStateEntity object
         */
        public DcUserOauthStateEntity build() {
            return new DcUserOauthStateEntity(
                    this.oauthState, 
                    this.signInId, 
                    this.expiresAt
            );
        }

        /**
         * Set oauthState.
         *
         * @param oauthState OAuth状態コード
         * @return this builder
         */
        public Builder oauthState(OAuthState oauthState) {
            this.oauthState = oauthState;
            return this;
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set expiresAt.
         *
         * @param expiresAt 有効期限
         * @return this builder
         */
        public Builder expiresAt(AppTimeStamp expiresAt) {
            this.expiresAt = expiresAt;
            return this;
        }
    }
}
// @formatter:on
