#!/usr/bin/env bash
# BZ口座開設完了イベントのテスト用スクリプト
# set -euo pipefail

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

enqueue() {
    local BODY=$1
    DEDUPLICATION_ID=$(shuf -i 1-100 -n 1)
    aws --endpoint-url=http://localhost:14566 sqs send-message \
        --queue-url http://localhost:14566/000000000000/dcjpy_bctracker_queue_push-notification.fifo \
        --message-body "${BODY}" \
        --message-deduplication-id "${DEDUPLICATION_ID}" \
        --message-group-id "group-id-2"
}
enqueue '{
            "Type": "Notification",
          	"MessageId": "b1f3c9d8-8096-5d85-b1a8-975e2ce4fd9f",
          	"SequenceNumber": "10000000000000019000",
          	"TopicArn": "arn:aws:sns:ap-northeast-1:2952********:prod-biz-a-tokyo-bcmonitoring-stream.fifo",
          	"Message": "{\"eventID\":\"b22df106815017077a65623fb473e0b8\",\"eventName\":\"INSERT\",\"eventVersion\":\"1.1\",\"eventSource\":\"aws:dynamodb\",\"awsRegion\":\"ap-northeast-1\",\"dynamodb\":{\"ApproximateCreationDateTime\":**********,\"Keys\":{\"logIndex\":{\"N\":\"0\"},\"transactionHash\":{\"S\":\"0xb1ca2377eb2b6da2f708beec649c778d0f806a4a3339cf7f5227f0390a6a727a\"}},\"NewImage\":{\"logIndex\":{\"N\":\"0\"},\"nonIndexedValues\":{\"S\":\"{\\\"accountId\\\":[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],\\\"accountStatus\\\":\\\"applying\\\",\\\"traceId\\\":[54,98,50,48,49,100,53,51,57,48,48,102,54,53,52,53,97,98,49,57,98,100,57,53,102,54,100,101,100,48,102,98],\\\"zoneId\\\":3001}\"},\"indexedValues\":{\"S\":\"{\\\"validatorId\\\":[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]}\"},\"log\":{\"S\":\"{\\\"address\\\":\\\"0xec2f490ed9ba9c619e591fbcd7f972023b47fe6f\\\",\\\"topics\\\":[\\\"0x1f9541f790c3a94103b661ae95ff701b0a1e9c2a7275f8499aa7ca97997fdd9f\\\"],\\\"data\\\":\\\"0x363036794e68654e5a70764c386e634b61676161756e43687968704e36684c5a0000000000000000000000000000000000000000000000000000000000000bb90000000000000000000000000000000000000000000000000000000000000080366232303164353339303066363534356162313962643935663664656430666200000000000000000000000000000000000000000000000000000000000000086170706c79696e67000000000000000000000000000000000000000000000000\\\",\\\"blockNumber\\\":\\\"0x10a4b3\\\",\\\"transactionHash\\\":\\\"0xb1ca2377eb2b6da2f708beec649c778d0f806a4a3339cf7f5227f0390a6a727a\\\",\\\"transactionIndex\\\":\\\"0x0\\\",\\\"blockHash\\\":\\\"0xa20c13269c0ef937fc6d697145a0286d9db0ef299dd2b2e15d9c0fa1b2c9d383\\\",\\\"logIndex\\\":\\\"0x0\\\",\\\"removed\\\":false}\"},\"name\":{\"S\":\"SyncBusinessZoneStatus\"},\"blockTimestamp\":{\"N\":\"**********\"},\"transactionHash\":{\"S\":\"0xb1ca2377eb2b6da2f708beec649c778d0f806a4a3339cf7f5227f0390a6a727a\"}},\"SequenceNumber\":\"167178300000000059399765174\",\"SizeBytes\":1325,\"StreamViewType\":\"NEW_AND_OLD_IMAGES\"},\"eventSourceARN\":\"arn:aws:dynamodb:ap-northeast-1:020545302498:table/Events/stream/2024-03-04T03:07:10.086\"}",
            "Timestamp": "2024-04-03T01:00:34.433Z",
            "UnsubscribeURL": "https://sns.ap-northeast-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:ap-northeast-1:2952********:prod-biz-a-tokyo-bcmonitoring-stream.fifo:8d1a841d-ca35-4ae2-869b-4d4e9eb1a48c"
             }'
