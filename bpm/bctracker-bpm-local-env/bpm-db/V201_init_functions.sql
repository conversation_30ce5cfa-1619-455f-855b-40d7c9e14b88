
-- dc_bank_numberを作成するためのシーケンス関数
CREATE OR REPLACE FUNCTION next_dc_bank_sequence()
    RETURNS BIGINT AS $$
DECLARE
    current_value BIGINT;
    next_value BIGINT;
BEGIN
    -- セッションレベルの排他的ロックを取得
    PERFORM pg_advisory_lock(101);

    SELECT nextval('seq_dc_bank_number') INTO current_value;
    next_value := current_value;

    -- 下4桁が全て同じ数字の場合
    IF next_value % 10000 = 9999 THEN
        next_value := next_value + 2;
    ELSEIF next_value % 10000 = (next_value % 10) * 1111 THEN
        next_value := next_value + 1;
    END IF;

    -- 上４桁が同じ数字の場合
    IF next_value / 10000 = next_value / ******** * 1111 THEN
        next_value := next_value + 10000;
    END IF;

    IF next_value != current_value THEN
        PERFORM setval('seq_dc_bank_number', next_value);
    END IF;

    -- セッションレベルの排他的ロックを開放
    PERFORM pg_advisory_unlock(101);

    RETURN next_value;
END;
$$ LANGUAGE plpgsql;
