#!/usr/bin/env bash

# set -euo pipefail

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

create_queue() {
    local QUEUE_NAME=$1
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
      sqs create-queue --queue-name "${QUEUE_NAME}" \
      --region ${AWS_REGION} \
      --attributes FifoQueue=true,VisibilityTimeout=30

    # dead letter queueを作成
    # 注意：dead letter queueもfifoにすること
    awslocal sqs create-queue --queue-name dead-letter-queue.fifo --attributes FifoQueue=true

    # 作成したqueueの読み込みが、4回失敗した場合、dead letter queueに送信するよう設定
    awslocal sqs set-queue-attributes \
    --queue-url http://sqs.ap-northeast-1.localhost.localstack.cloud:4566/000000000000/"${QUEUE_NAME}" \
    --attributes '{
      "RedrivePolicy": "{\"deadLetterTargetArn\":\"arn:aws:sqs:us-east-1:000000000000:dead-letter-queue.fifo\",\"maxReceiveCount\":\"4\"}"
    }'
}

verify_email_address(){
  local EMAIL_ADDRESS=$1
      awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
        ses verify-email-identity --email-address "${EMAIL_ADDRESS}" \
        --region ${AWS_REGION}
}

echo "Start to configure SQS."

create_queue "dcjpy_bctracker_queue_push-notification.fifo"
create_queue "dcjpy_bctracker_queue_email-sender.fifo"

verify_email_address "<EMAIL>"
