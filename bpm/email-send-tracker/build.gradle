// bpm / email-send-tracker
buildscript {
    dependencies {
        classpath "org.postgresql:postgresql:42.5.0"
    }
}

plugins {
    id 'java'
    id 'groovy'
    id 'org.springframework.boot' version '2.7.6'
    id 'io.spring.dependency-management' version '1.0.13.RELEASE'
    id "com.diffplug.eclipse.apt" version "3.38.0"
    id "org.domaframework.doma.compile" version "2.0.0"
    id "org.domaframework.doma.codegen" version "2.0.0"
    id 'jacoco'

    id "eclipse"
    id "idea"
}

apply from: rootProject.file('build.common.gradle')

group = 'com.decurret_dcp'
version = '0.0.1-SNAPSHOT'


repositories {
    mavenCentral()
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

dependencies {
    implementation project(':bctracker-base')
    implementation project(':bpm:bpm-repository')

    implementation 'software.amazon.awssdk:ses'
}
