package com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.application;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.base.application.BaseTracker;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.service.EmailSendMessage;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.value.EmailSenderMessage;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.service.ValidatorFilter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * メールによるユーザ通知を行うトラッカー.
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class EMailSendTracker implements BaseTracker<EmailSenderMessage> {

    private final ValidatorFilter validatorFilter;

    private final EmailSendMessage emailSendMessage;

    @Override
    public EmailSenderMessage acceptable(BCEvent<?> event) {
        EmailSenderMessage message = EmailSenderMessage.create(event.to());
        if (message == null) {
            return null;
        }

        boolean acceptable = this.validatorFilter.acceptable(message.validatorId());
        if (acceptable == false) {
            return null;
        }

        return message;
    }

    @Override
    public boolean onMessage(EmailSenderMessage message) {
        this.emailSendMessage.sendEmailTransfer(message);

        log.info("Send transaction completed email. accountId : {}, zoneId : {}, transactionHash : {}",
                 message.accountId.getValue(), message.zoneId.getValue(), message.transactionHash.getValue());

        return true;
    }
}
