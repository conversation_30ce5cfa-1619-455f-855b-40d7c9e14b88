package com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.config.EMailSendTrackerProperty;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.service.command.TransferSenderCommand;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.value.EmailSenderMessage;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcAccountEmailEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.repository.DcAccountRepository;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.service.MessageBuilder;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.MessageKeywordPair;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Transactional
@AllArgsConstructor
@Service
@Slf4j
public class EmailSendMessage {

    private final EMailSendTrackerProperty property;

    private final MessageBuilder messageBuilder;

    private final EMailSender eMailSender;

    private final DcAccountRepository dcAccountRepository;

    public void sendEmailTransfer(EmailSenderMessage message) {

        // メッセージ本文取得
        List<MessageKeywordPair> keywordPairs = List.of(
                MessageKeywordPair.Keyword.CONTACT_EMAIL_ADDRESS.with(property.contactEmailAddress),
                MessageKeywordPair.Keyword.TRANSFER_AT.with(message.transferAt)
        );
        String messageBody = this.messageBuilder.buildMessage(TemplateKey.EMAIL_TRANSFER, keywordPairs);

        // 宛先取得
        DcAccountEmailEntity dcAccountEmail =
                this.dcAccountRepository.findDcAccountEmail(message.accountId, message.validatorId());
        if (dcAccountEmail == null) {
            log.error("Email address for dcAccount is not found. accountId : {}, transactionHash : {}",
                      message.accountId.getValue(), message.transactionHash.getValue());
            throw new RuntimeException("Email address not exists. accountId = " + message.accountId.getValue());
        }

        TransferSenderCommand command = TransferSenderCommand.builder()
                .sendEmailAddress(dcAccountEmail.emailAddress)
                .message(messageBody)
                .subject(message.subject())
                .build();

        try {
            // メール送信
            this.eMailSender.sendMessage(command);
        } catch (RuntimeException exc) {
            log.error("Failed to send transaction completed email. accountId : {}, zoneId : {}, "
                              + "transactionHash : {}, detail : {}",
                      message.accountId.getValue(), message.zoneId.getValue(), message.transactionHash.getValue(),
                      exc.getMessage());

            throw exc;
        }
    }
}
