package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper;

public class UniCodeHelper {
    private UniCodeHelper() {
        // Do nothing
    }

    /**
     * ユニコードの文字列をエスケープします。
     *
     * 例:"\u3042"を"あ"に変換します。
     *
     * @param unicodeString ユニコードの文字列
     *
     * @return ユニコードをエスケープした文字列
     */
    public static String escape(String unicodeString) {
        StringBuilder result = new StringBuilder();
        int len = unicodeString.length();
        for (int i = 0; i < len; i++) {
            if (unicodeString.charAt(i) == '\\' && i + 5 < len && unicodeString.charAt(i + 1) == 'u') {
                String hex = unicodeString.substring(i + 2, i + 6);
                int codePoint = Integer.parseInt(hex, 16);
                result.append((char) codePoint);
                i += 5;
            } else {
                result.append(unicodeString.charAt(i));
            }
        }
        return result.toString();
    }
}
