package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper

import groovy.sql.Sql
import org.springframework.test.context.DynamicPropertyRegistry
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest
import software.amazon.awssdk.services.sqs.model.QueueAttributeName

class BpmAdhocHelper {

    private static final int DB_POOL_CONNECTION_IDLE_SIZE = 2

    private static final ObjectMapper mapper = new ObjectMapper()

    private static dbPort

    private static localStackPort

    private static DockerComposeContainer composeContainer

    private static connectionCount = 0

    static Sql sql

    static SqsClient sqsClient

    static String queueUri

    static String getDbPort() { return dbPort }

    static String getLocalStackPort() { return localStackPort }

    static {
        startContainer()
        createSQS("dcjpy_bctracker_queue_push-notification.fifo")
    }

    private static void startContainer() {
        // dbが起動した後にpostgresにアクセスできるように、Wait.forLogMessage()でアクセス可能になるログが出力されるまで待つ
        composeContainer = new DockerComposeContainer(new File("../docker-compose_cicd.yml"))
                .withExposedService("db", 5432)
                .withExposedService("localstack", 4566)
                .waitingFor("db", Wait.forListeningPort())
                .waitingFor("localstack", Wait.forListeningPort())
                .waitingFor("db", Wait.forLogMessage(".*database system is ready to accept connections.*\\n", 1))

        composeContainer.start()
        dbPort = String.valueOf(composeContainer.getServicePort("db", 5432))
        localStackPort = String.valueOf(composeContainer.getServicePort("localstack", 4566))
    }

    static Sql initAdhoc(DynamicPropertyRegistry registry) {
        var jdbcUrl = "***************************:${dbPort}/postgres"

        registry.add("spring.datasource.url", () -> jdbcUrl)
        registry.add("bctracker.base.sqs.local-endpoint", () -> "http://localhost:${getLocalStackPort()}")
        registry.add("bctracker.push-notification.sns-local-endpoint", () -> "http://localhost:${getLocalStackPort()}")

        // テストではコネクションを多く用意する必要はないので、少なめにする
        registry.add("spring.datasource.hikari.minimum-idle", () -> DB_POOL_CONNECTION_IDLE_SIZE)

        sql = Sql.newInstance(jdbcUrl, "bpm_user", "bpm_password", "org.postgresql.Driver")

        sql.execute("DROP SCHEMA if EXISTS PUBLIC CASCADE")
        sql.execute("CREATE SCHEMA public")
        // DDL およびマスタデータの投入
        sql.execute(new File("../bctracker-bpm-local-env/bpm-db/V001_bpm-server.sql").text)
        sql.execute(new File("../bctracker-bpm-local-env/bpm-db/V101_init_master-data.sql").text)
        sql.execute(new File("../bctracker-bpm-local-env/bpm-db/V201_init_functions.sql").text)
        // テスト用データの投入
        sql.execute(new File("../bctracker-bpm-local-env/bpm-db/V901_init_test-data.sql").text)

        return sql
    }

    static void cleanupSpec() {
        sql.close()
        sqsClient.close()

        // テストクラスごとにSpringBootを起動しているにも関わらず、
        // なぜか以前のテストケースでのDBコネクションが残ったままなので、
        // デフォルトのDBコネクションプールのサイズを超えたら、コンテナを再起動する
        connectionCount += DB_POOL_CONNECTION_IDLE_SIZE
        if (connectionCount >= 100) {
            composeContainer.stop()
            connectionCount = 0

            startContainer()
        }
    }

    private static void createSQS(String queueName) {
        sqsClient = SqsClient.builder()
                .region(Region.AP_NORTHEAST_1)
                .endpointOverride(URI.create("http://localhost:${localStackPort}"))
                .build()

        Map<QueueAttributeName, String> createQueueParams = new HashMap<>()
        createQueueParams.put(QueueAttributeName.FIFO_QUEUE, "true")
        createQueueParams.put(QueueAttributeName.VISIBILITY_TIMEOUT, "30")
        createQueueParams.put(QueueAttributeName.CONTENT_BASED_DEDUPLICATION, "true")

        def createQueueRequest = CreateQueueRequest.builder()
                .queueName(queueName)
                .attributes(createQueueParams)
                .build()

        def response = sqsClient.createQueue(createQueueRequest)
        queueUri = response.queueUrl()

        sqsClient.close()
    }
}
