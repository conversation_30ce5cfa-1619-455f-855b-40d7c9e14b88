package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.config;

import lombok.AllArgsConstructor;
import lombok.ToString;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.util.StringUtils;

@ConstructorBinding
@ConfigurationProperties(prefix = "bctracker.push-notification")
@AllArgsConstructor
@ToString
public class PushNotificationTrackerProperty {

    /** SNS のローカル環境への接続先。 */
    public final String snsLocalEndpoint;

    /**
     * AWS 環境の SNS に接続するかどうか。
     *
     * @return AWS 環境の SNS に接続する場合 true
     */
    public boolean toAwsConnection() {
        return (StringUtils.hasText(this.snsLocalEndpoint) == false);
    }
}
