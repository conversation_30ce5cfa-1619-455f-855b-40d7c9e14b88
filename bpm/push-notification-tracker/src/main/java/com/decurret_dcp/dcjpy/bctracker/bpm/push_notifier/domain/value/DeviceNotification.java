package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value;

import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.service.command.PushNotificationCommand;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserDeviceEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserNotificationEntity;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class DeviceNotification {

    public final DcUserNotificationEntity notification;

    public final DcUserDeviceEntity userDevice;

    public PushNotificationCommand initPushCommand() {
        return PushNotificationCommand.builder()
                .notificationId(this.notification.notificationId)
                .signInId(this.notification.signInId)
                .message(this.notification.message)
                .device(this.userDevice)
                .build();
    }
}
