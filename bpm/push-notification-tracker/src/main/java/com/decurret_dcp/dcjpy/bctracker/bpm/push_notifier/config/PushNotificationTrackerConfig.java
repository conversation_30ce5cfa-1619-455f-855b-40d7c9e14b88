package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.config;

import java.net.URI;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.SnsClientBuilder;

@Configuration
@Slf4j
public class PushNotificationTrackerConfig {

    @Bean
    public SnsClient snsClient(PushNotificationTrackerProperty property) {
        SnsClientBuilder builder = SnsClient.builder();

        if (property.toAwsConnection() == false) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(property.snsLocalEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));

            log.error(
                    "Change the SNS connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    property.snsLocalEndpoint
            );
        }

        return builder.build();
    }
}
