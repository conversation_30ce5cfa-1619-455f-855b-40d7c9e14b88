package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value.PushNotificationMessage;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserDeviceEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserNotificationEntity;

public interface DcUserNotificationRepository {

    public DcUserNotificationEntity save(PushNotificationMessage event, SignInId signInId, String message);

    public DcUserDeviceEntity findDevice(SignInId signInId);
}
