package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.application;

import com.decurret_dcp.dcjpy.bctracker.base.application.BaseTracker;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.repository.DcUserNotificationRepository;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.service.MessageNotifier;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.service.command.PushNotificationCommand;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value.DeviceNotification;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value.PushNotificationMessage;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserDeviceEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserNotificationEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.repository.DcAccountRepository;
import com.decurret_dcp.dcjpy.bctracker.base.domain.repository.TransactionSupport;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.service.MessageBuilder;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.service.ValidatorFilter;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.MessageKeywordPair;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * スマートフォンアプリへのプッシュによるユーザ通知を行うトラッカー.
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PushNotificationTracker implements BaseTracker<PushNotificationMessage> {

    private final MessageBuilder messageBuilder;

    private final MessageNotifier messageNotifier;

    private final ValidatorFilter validatorFilter;

    private final TransactionSupport transactionSupport;

    private final DcAccountRepository dcAccountRepository;

    private final DcUserNotificationRepository dcUserNotificationRepository;

    @Override
    public PushNotificationMessage acceptable(BCEvent<?> event) {
        PushNotificationMessage message = PushNotificationMessage.create(event.to());
        if (message == null) {
            return null;
        }

        boolean acceptable = this.validatorFilter.acceptable(message.validatorId());
        if (acceptable == false) {
            return null;
        }

        return message;
    }

    @Override
    public boolean onMessage(PushNotificationMessage message) {
        DeviceNotification deviceNotification = this.transactionSupport.transaction(
                () -> {
                    DcUserEntity accountOwner = this.dcAccountRepository.findAccountOwner(
                            message.accountId(), message.validatorId());
                    if (accountOwner == null) {
                        throw new IllegalStateException("AccountOwner is not found. message = " + message);
                    }

                    String content = this.initMessage(message);
                    DcUserNotificationEntity notification =
                            this.dcUserNotificationRepository.save(message, accountOwner.signInId, content);

                    DcUserDeviceEntity userDevice =
                            this.dcUserNotificationRepository.findDevice(accountOwner.signInId);

                    return DeviceNotification.builder()
                            .notification(notification)
                            .userDevice(userDevice)
                            .build();
                }
        );

        DcUserNotificationEntity notification = deviceNotification.notification;
        log.info("Save push notification message. notificationId = {}, signInId = {}",
                 notification.notificationId.getValue(), notification.signInId.getValue());

        PushNotificationCommand pushCommand = deviceNotification.initPushCommand();
        this.messageNotifier.publish(pushCommand);

        return true;
    }

    private String initMessage(PushNotificationMessage message) {
        List<MessageKeywordPair> keywordPairs = message.keywordPairs();
        TemplateKey templateKey = message.messageKey();

        return this.messageBuilder.buildMessage(templateKey, keywordPairs);
    }
}
