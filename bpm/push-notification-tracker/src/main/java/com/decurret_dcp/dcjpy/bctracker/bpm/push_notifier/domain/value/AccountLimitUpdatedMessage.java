package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ModTokenLimitEvent;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.MessageKeywordPair;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class AccountLimitUpdatedMessage implements PushNotificationMessage {

    /** トランザクションハッシュ. */
    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimestamp;

    public final ValidatorId validatorId;

    /** アカウントID. */
    public final AccountId accountId;

    /** 更新日時. */
    public final AppTimeStamp updatedAt;

    static AccountLimitUpdatedMessage create(ModTokenLimitEvent event) {
        return AccountLimitUpdatedMessage.builder()
                .transactionHash(event.transactionHash)
                .blockTimestamp(event.blockTimeStamp)
                .validatorId(event.validatorId)
                .accountId(event.accountId)
                .updatedAt(event.blockTimeStamp)
                .build();
    }

    @Override
    public TransactionHash transactionHash() {
        return this.transactionHash;
    }

    @Override
    public AppTimeStamp blockTimestamp() {
        return this.blockTimestamp;
    }

    @Override
    public AccountId accountId() {
        return this.accountId;
    }

    @Override
    public ValidatorId validatorId() {
        return this.validatorId;
    }

    @Override
    public TemplateKey messageKey() {
        return TemplateKey.ACCOUNT_UPDATED;
    }

    @Override
    public List<MessageKeywordPair> keywordPairs() {
        return List.of(
                MessageKeywordPair.Keyword.COMPLETED_AT.with(this.updatedAt.format(DATE_FORMATTER))
        );
    }

    @Override
    public NotificationType notificationType() {
        return NotificationType.ACCOUNT_UPDATED;
    }

    @Override
    public NotificationDetail notificationDetail() {
        return NotificationDetail.of(
                Map.ofEntries(
                        Map.entry("transaction_hash", this.transactionHash().getValue())
                )
        );
    }
}
