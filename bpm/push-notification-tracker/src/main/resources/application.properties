# SpringBoot ã®ããã¼ãè¡¨ç¤ºããªãããã«ãã
spring.main.banner-mode=off

# ã­ã°ã®ãã©ã¼ããã
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(\\(%32.32X{traceId}\\)){magenta} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx

##########################################################################
# DB Connection
##########################################################################
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=jdbc:postgresql://${DB_BASE:localhost}:${DB_PORT:15432}/${DB_NAME:bpm_db}
spring.datasource.username=${DB_USER:bpm_user}
spring.datasource.password=${DB_PASS:bpm_password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.maximum-pool-size=${DB_MAXIMUM_POOL_SIZE:50}
spring.datasource.hikari.minimum-idle=${DB_MINIMUM_IDLE:10}
# ãã¼ã«åã®ã³ãã¯ã·ã§ã³ã DB ã«æ¥ç¶ããã¿ã¤ã ã¢ã¦ãæé (ããªç§)
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
# ãã¼ã«åã®ã¢ã¤ãã«ç¶æã®ã³ãã¯ã·ã§ã³ã®ã¿ã¤ã ã¢ã¦ãæé (ããªç§)
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}

##########################################################################
# JACKSON
##########################################################################
# JSON ã®ãã©ã¡ã¼ã¿åã snake_case ã¨ãã¦æ±ã
spring.jackson.property-naming-strategy=SNAKE_CASE
# JSON ã®æ¥æå½¢å¼ãæ¨æºå½¢å¼ã¨ãã¦æ±ã
spring.jackson.serialization.write-dates-as-timestamps=false

##########################################################################
# BCTracker å±éæ©è½ (SQS)
##########################################################################
# æ±ãã¤ãã³ããæ ¼ç´ãããã­ã¥ã¼åã
bctracker.base.sqs.queue-name=${PUSH_NOTIFICATION_SQS_QUEUE_NAME:dcjpy_bctracker_queue_push-notification.fifo}
# å¯è¦æ§ã¿ã¤ã ã¢ã¦ãæé (ç§)
bctracker.base.sqs.visibility-timeout=${BASE_VISIBILITY_TIMEOUT:5}
# ãã¼ãªã³ã°ã¡ãã»ã¼ã¸å¾æ©æé (ç§)
bctracker.base.sqs.wait-time-seconds=${BASE_WAIT_TIME_SECONDS:1}
# ã­ã¼ã«ã«ç°å¢ã¸ã®æ¥ç¶å
bctracker.base.sqs.local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:14566}

##########################################################################
# BCTracker Push éç¥
##########################################################################
# SNS ã®ã­ã¼ã«ã«ç°å¢ã¸ã®æ¥ç¶å
bctracker.push-notification.sns-local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:14566}
