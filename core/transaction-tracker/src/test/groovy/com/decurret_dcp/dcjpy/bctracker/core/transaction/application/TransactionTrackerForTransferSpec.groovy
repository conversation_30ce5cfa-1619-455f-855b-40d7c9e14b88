package com.decurret_dcp.dcjpy.bctracker.core.transaction.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionTransferMessage
import com.decurret_dcp.dcjpy.bctracker.core.transaction.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest()
class TransactionTrackerForTransferSpec extends Specification {

    @Autowired
    TransactionTracker transactionTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        // SQSのQueueを作成しないと、起動時にエラーになる。
        sqsClient = CoreAdhocHelper.createSQS("dcjpy_bctracker_queue_transaction.fifo")
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    private static JsonNode getTransferBizZoneIndexValues() {
        String indexValues = """
            {}
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * Transfer(Charge)イベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return Transfer(Charge)イベントのindexValuesの値
     */
    private static JsonNode getTransferChargeIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * Transfer(Discharge)イベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return Transfer(Discharge)イベントのindexValuesの値
     */
    private static JsonNode getTransferDischargeIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * Transfer(Discharge)イベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return Transfer(Discharge)イベントのnonIndexValuesの値
     */
    private static JsonNode getTransferDischargeNonIndexValues() {
        String indexValues = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "0",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                }
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * Transfer(Charge)イベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return Transfer(Charge)イベントのnonIndexValuesの値
     */
    private static JsonNode getTransferChargeNonIndexValues() {
        String indexValues = """
            {
                "transferData": {
                    "transferType": [99,104,97,114,103,101 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "0",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,97,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,49,98,98,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                }
            }
        """
        return objectMapper.readTree(indexValues)
    }

    def "testAcceptable_TransferイベントでvalidatorIdがclient entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "toValidatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_Transfer(BizZone)イベントが発生した場合_ #testCase"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": ${toValidatorId},
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": ${sendAccountId},
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals(validatorId)

        where:
        testCase                                                                      | toValidatorId                                                                                             | sendAccountId                                                                                                                          | validatorId
        "from_validatorとto_validator, from_accountとsend_accountが異なる場合"        | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb"
        "from_validatorとto_validatorが同じで、from_accountとsend_accountが異なる場合" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
        "from_validatorとto_validatorが異なり、form_accountとsend_accountが同じ場合"   | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb"
        "from_validatorとto_validator、from_accountとsend_accountが同じ場合"           | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
    }

    def "testAcceptable_Transfer(FinZone)イベントが発生した場合_ #testCase"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": ${toValidatorId},
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": ${sendAccountId},
                "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals(validatorId1)

        where:
        testCase                                                                      | toValidatorId                                                                                             | sendAccountId                                                                                                                          | validatorId1
        "from_validatorとto_validator, from_accountとsend_accountが異なる場合"        | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb"
        "from_validatorとto_validatorが同じで、from_accountとsend_accountが異なる場合" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
        "from_validatorとto_validatorが異なり、form_accountとsend_accountが同じ場合"   | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb"
        "from_validatorとto_validator、from_accountとsend_accountが同じ場合"           | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
    }

    def "testAcceptable_Transfer(Charge)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferChargeIndexValues()
        JsonNode nonIndexValues = getTransferChargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
    }

    def "testAcceptable_Transfer(Charge)イベントが発生した場合_miscValue2が複数"() {
        setup:
        JsonNode indexValues = getTransferChargeIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [99,104,97,114,103,101 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "0",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,97,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,49,98,98,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5,310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p6",
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
    }

    def "testAcceptable_Transfer(Charge)イベントが発生した場合_miscValue2が4096文字"() {
        setup:
        JsonNode indexValues = getTransferChargeIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [99,104,97,114,103,101 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "0",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,97,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,49,98,98,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "${'a' * 4096}",
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
    }

    def "testAcceptable_Transfer(Discharge)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        JsonNode nonIndexValues = getTransferDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
    }

    def "testAcceptable_Transfer(Discharge)イベントが発生した場合_miscValue2が複数"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "0",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
    }

    def "testAcceptable_Transfer(Discharge)イベントが発生した場合_miscValue2が4096文字"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "0",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "${'a' * 4096}",
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.TRANSFER
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.validatorIds().get(1).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生し、from_accountとsend_account、from_validatorIdとto_validatorIdが異なる場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)
        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def memoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")


        Objects.isNull(toAccountRecord)
        memoRecord.size() == 1
        miscRecord.size() == 1
        senderRecord.size() == 1


        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)

    }

    def "testOnMessage_Transfer(BizZone)イベントが発生し、from_accountとsend_accountが異なり、from_validatorIdととto_validatorIdが同じ場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)
        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")
        def toAccountTransactionId = toAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def toMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$toAccountTransactionId';
        """
        )

        def toMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$toAccountTransactionId';
        """)

        def toSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$toAccountTransactionId';
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        !Objects.isNull(toAccountRecord)
        toAccountRecord.get("transaction_id").equals(toAccountTransactionId)
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("アカウント2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3001
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("transfer")
        toAccountRecord.get("amount") == 100
        toAccountRecord.get("post_balance") == 20000
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        toAccountRecord.get("other_account_name").equals("アカウント1")
        Objects.isNull(toAccountRecord.get("other_zone_id"))

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        !Objects.isNull(toMemoRecord)
        toMemoRecord.get("transaction_id").equals(toAccountTransactionId)
        toMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(toMiscRecord)
        toMiscRecord.get("transaction_id").equals(toAccountTransactionId)
        toMiscRecord.get("misc_value1").equals("100")
        toMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(toSenderRecord)
        toSenderRecord.get("transaction_id").equals(toAccountTransactionId)
        toSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生し、form_accountとsend_accountが同じで、from_validatorIdとto_validatorIdが異なる場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)
        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        Objects.isNull(fromSenderRecord)

        allMemoRecord.size() == 1
        allMiscRecord.size() == 1
        allSenderRecord.size() == 0

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)

    }

    def "testOnMessage_Transfer(BizZone)イベントが発生し、form_accountとsend_account、from_validatorとto_validatorが同じ場合 #testCase"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)
        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")
        def toAccountTransactionId = toAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def toMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$toAccountTransactionId';
        """
        )

        def toMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$toAccountTransactionId';
        """)

        def toSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$toAccountTransactionId';
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        !Objects.isNull(toAccountRecord)
        toAccountRecord.get("transaction_id").equals(toAccountTransactionId)
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("アカウント2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3001
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("transfer")
        toAccountRecord.get("amount") == 100
        toAccountRecord.get("post_balance") == 20000
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        toAccountRecord.get("other_account_name").equals("アカウント1")
        Objects.isNull(toAccountRecord.get("other_zone_id"))

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        Objects.isNull(fromSenderRecord)

        !Objects.isNull(toMemoRecord)
        toMemoRecord.get("transaction_id").equals(toAccountTransactionId)
        toMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(toMiscRecord)
        toMiscRecord.get("transaction_id").equals(toAccountTransactionId)
        toMiscRecord.get("misc_value1").equals("100")
        toMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        Objects.isNull(toSenderRecord)

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(FinZone)イベントが発生し、from_accountとsend_account、from_validatorとto_validatorが異なる場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId":  [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3000';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3000';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3000
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecord.size() == 1
        allMiscRecord.size() == 1
        allSenderRecord.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(FinZone)イベントが発生し、from_accountとsend_accountが異なりfrom_validatorとto_validatorが同じ場合_ #testCase"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId":  [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3000';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3000';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")
        def toAccountTransactionId = toAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def toMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$toAccountTransactionId';
        """
        )

        def toMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$toAccountTransactionId';
        """)

        def toSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$toAccountTransactionId';
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3000
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        !Objects.isNull(toAccountRecord)
        toAccountRecord.get("transaction_id").equals(toAccountTransactionId)
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("アカウント2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3000
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("transfer")
        toAccountRecord.get("amount") == 100
        toAccountRecord.get("post_balance") == 20000
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        toAccountRecord.get("other_account_name").equals("アカウント1")
        Objects.isNull(toAccountRecord.get("other_zone_id"))

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        !Objects.isNull(toMemoRecord)
        toMemoRecord.get("transaction_id").equals(toAccountTransactionId)
        toMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(toMiscRecord)
        toMiscRecord.get("transaction_id").equals(toAccountTransactionId)
        toMiscRecord.get("misc_value1").equals("100")
        toMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(toSenderRecord)
        toSenderRecord.get("transaction_id").equals(toAccountTransactionId)
        toSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(FinZone)イベントが発生し、form_accountとsend_accountが同じ、from_validatorとto_validatoが異なる場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3000';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3000';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3000
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        Objects.isNull(fromSenderRecord)

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 0

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(FinZone)イベントが発生し、form_accountとsend_account、from_validatorとto_validatorが同じ場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3000';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3000';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")
        def toAccountTransactionId = toAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def toMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$toAccountTransactionId';
        """
        )

        def toMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$toAccountTransactionId';
        """)

        def toSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$toAccountTransactionId';
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3000
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        !Objects.isNull(toAccountRecord)
        toAccountRecord.get("transaction_id").equals(toAccountTransactionId)
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("アカウント2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3000
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("transfer")
        toAccountRecord.get("amount") == 100
        toAccountRecord.get("post_balance") == 20000
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        toAccountRecord.get("other_account_name").equals("アカウント1")
        Objects.isNull(toAccountRecord.get("other_zone_id"))

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        Objects.isNull(fromSenderRecord)

        !Objects.isNull(toMemoRecord)
        toMemoRecord.get("transaction_id").equals(toAccountTransactionId)
        toMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(toMiscRecord)
        toMiscRecord.get("transaction_id").equals(toAccountTransactionId)
        toMiscRecord.get("misc_value1").equals("100")
        toMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        Objects.isNull(toSenderRecord)

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)

    }

    def "testOnMessage_Transfer(FinZone)イベントが発生し、from_validatorがclient_entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3000';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3000';
        """)

        def toAccountTransactionId = toAccountRecord.get("transaction_id")

        def toMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$toAccountTransactionId';
        """
        )

        def toMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$toAccountTransactionId';
        """)

        def toSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$toAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        Objects.isNull(fromAccountRecord)

        !Objects.isNull(toAccountRecord)
        toAccountRecord.get("transaction_id").equals(toAccountTransactionId)
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("アカウント2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3000
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("transfer")
        toAccountRecord.get("amount") == 100
        toAccountRecord.get("post_balance") == 20000
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        toAccountRecord.get("other_account_name").equals("アカウント1")
        Objects.isNull(toAccountRecord.get("other_zone_id"))

        !Objects.isNull(toMemoRecord)
        toMemoRecord.get("transaction_id").equals(toAccountTransactionId)
        toMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(toMiscRecord)
        toMiscRecord.get("transaction_id").equals(toAccountTransactionId)
        toMiscRecord.get("misc_value1").equals("100")
        toMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        Objects.isNull(toSenderRecord)

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 0

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)

    }


    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue1がnullの場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": null,
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        Objects.isNull(fromMiscRecord.get("misc_value1"))
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue2がnullの場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": null,
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        Objects.isNull(fromMiscRecord.get("misc_value2"))

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue2が複数の場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5,310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p6",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5,310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p6")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue2が4096文字の場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "${'a' * 4096}",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals('a' * 4096)

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue1とmiscValue2がnullの場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": null,
                "miscValue2": null,
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        Objects.isNull(fromMiscRecord)

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 0
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue1が空文字の場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": "",
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        Objects.isNull(fromMiscRecord.get("misc_value1"))
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue2が空文字の場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        Objects.isNull(fromMiscRecord.get("misc_value2"))

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)

    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmiscValue1とmiscValue2が空文字の場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": "",
                "miscValue2": "",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        !Objects.isNull(fromMemoRecord)
        fromMemoRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMemoRecord.get("memo").equals("メモmemo")

        Objects.isNull(fromMiscRecord)

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 1
        allMiscRecords.size() == 0
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction_memo;
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmemoの値がnullの場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": null},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)


        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        Objects.isNull(fromMemoRecord)

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 0
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(BizZone)イベントが発生しmemoの値が空文字の場合"() {
        setup:
        JsonNode indexValues = getTransferBizZoneIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3001",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97],
                "fromAccountId": [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": ""
            },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def fromMemoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo WHERE transaction_id = '$fromAccountTransactionId';
        """
        )

        def fromMiscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def fromSenderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender WHERE transaction_id = '$fromAccountTransactionId';
        """)

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("account_name").equals("アカウント1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 10000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("アカウント2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        Objects.isNull(fromMemoRecord)

        !Objects.isNull(fromMiscRecord)
        fromMiscRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromMiscRecord.get("misc_value1").equals("100")
        fromMiscRecord.get("misc_value2").equals("310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5")

        !Objects.isNull(fromSenderRecord)
        fromSenderRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromSenderRecord.get("send_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFba")

        allMemoRecords.size() == 0
        allMiscRecords.size() == 1
        allSenderRecords.size() == 1

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
            DELETE FROM transaction_misc;
            DELETE FROM transaction_sender;
        """)
    }

    def "testOnMessage_Transfer(Charge)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferChargeIndexValues()
        JsonNode nonIndexValues = getTransferChargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()
        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '61aOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3000';
        """)

        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '61aOvB8L3VxYSCM5QWd1WpSNenGaGFbb' AND zone_id = '3001';
        """)

        def toAccountTransactionId = toAccountRecord.get("transaction_id")
        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(toAccountRecord)
        toAccountRecord.get("transaction_id").equals(toAccountTransactionId)
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        toAccountRecord.get("account_id").equals("61aOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        toAccountRecord.get("account_name").equals("アカウント1")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3000
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("charge")
        toAccountRecord.get("amount") == -100
        toAccountRecord.get("post_balance") == 10000
        toAccountRecord.get("other_account_id").equals("61aOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(toAccountRecord.get("other_account_name"))
        toAccountRecord.get("other_zone_id") == 3001

        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        fromAccountRecord.get("account_id").equals("61aOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(fromAccountRecord.get("account_name"))
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("charge")
        fromAccountRecord.get("amount") == 100
        fromAccountRecord.get("post_balance") == 30000
        fromAccountRecord.get("other_account_id").equals("61aOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(fromAccountRecord.get("other_account_name"))
        fromAccountRecord.get("other_zone_id") == 3000

        allMemoRecords.size() == 0
        allMiscRecords.size() == 0
        allSenderRecords.size() == 0

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """)

    }

    def "testOnMessage_Transfer(Discharge)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        JsonNode nonIndexValues = getTransferDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()
        TransferEvent transferEvent = TransferEvent.create(testEvent)
        TransactionTransferMessage transactionMessage = TransactionTransferMessage.create(transferEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionMessage)
        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3000';
        """)

        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)

        def toAccountTransactionId = toAccountRecord.get("transaction_id")
        def fromAccountTransactionId = fromAccountRecord.get("transaction_id")

        def allMemoRecords = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def allMiscRecords = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def allSenderRecords = sql.rows("""
            SELECT * FROM transaction_sender;
        """)


        then:
        result == true
        !Objects.isNull(toAccountRecord)
        toAccountRecord.get("transaction_id").equals(toAccountTransactionId)
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("アカウント2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        toAccountRecord.get("zone_id") == 3000
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("discharge")
        toAccountRecord.get("amount") == 100
        toAccountRecord.get("post_balance") == 20000
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        Objects.isNull(toAccountRecord.get("other_account_name"))
        toAccountRecord.get("other_zone_id") == 3001

        !Objects.isNull(fromAccountRecord)
        fromAccountRecord.get("transaction_id").equals(fromAccountTransactionId)
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        Objects.isNull(fromAccountRecord.get("account_name"))
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("discharge")
        fromAccountRecord.get("amount") == -100
        fromAccountRecord.get("post_balance") == 30000
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        Objects.isNull(fromAccountRecord.get("other_account_name"))
        fromAccountRecord.get("other_zone_id") == 3000

        allMemoRecords.size() == 0
        allMiscRecords.size() == 0
        allSenderRecords.size() == 0

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """)

    }

}
