package com.decurret_dcp.dcjpy.bctracker.core.transaction.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountStatus
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ForceBurnEvent
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.value.ContractBytes32Value
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionForceBurnMessage
import com.decurret_dcp.dcjpy.bctracker.core.transaction.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson
import static com.github.tomakehurst.wiremock.client.WireMock.okJson
import static com.github.tomakehurst.wiremock.client.WireMock.post
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest()
class TransactionTrackerForForceBurnSpec extends Specification {

    @Autowired
    TransactionTracker transactionTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @Shared
    WireMockServer wiremockServer

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        // SQSのQueueを作成しないと、起動時にエラーになる。
        sqsClient = CoreAdhocHelper.createSQS("dcjpy_bctracker_queue_transaction.fifo")
        objectMapper = new ObjectMapper()

        wiremockServer = new WireMockServer(options().port(8081))
        wiremockServer.start()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
        wiremockServer.stop()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * ForceBurnイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ForceBurnイベントのindexValuesの値
     */
    private static JsonNode getForceBurnIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * ForceBurnイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ForceBurnイベントのnonIndexValuesの値
     */
    private static JsonNode getForceBurnNonIndexValues() {
        String nonIndexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "traceId": [55,55,55,55,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId":"3000",
                "burnedAmount": "8000",
                "burnedBalance": "0",
                "forceDischarge": [{"zoneId":"3001","dischargeAmount":"1000"},{"zoneId":"3002","dischargeAmount":"2000"}]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    private static JsonNode getForceBurnNonIndexValuesWithoutDischarge() {
        String nonIndexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "traceId": [55,55,55,55,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId":"3000",
                "burnedAmount": "1000",
                "burnedBalance": "0",
                "forceDischarge": [{"zoneId":"3001","dischargeAmount":"0"},{"zoneId":"3002","dischargeAmount":"0"}]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }


    static String initBCClientRequest(String zoneId, String contractName, String method, Map<String, Object> args) {
        Map<String, Object> requestCheckMap = Map.of(
                "zoneId", zoneId,
                "contractName", contractName,
                "method", method,
                "args", args
        )

        return objectMapper.writeValueAsString(requestCheckMap)
    }

    static void mockCallValidatorGetAccount(WireMockServer wiremockServer) {
        def active = (new ContractBytes32Value(AccountStatus.ACTIVE.getValue())).getValue()
        def accountIdHex = "0x363030374954467769556d48674f49353844394933627478336a536d77596553"

        def requestCheckData = initBCClientRequest("\${json-unit.any-string}", "Validator", "getAccount",
                Map.ofEntries(
                        Map.entry("validatorId", "\${json-unit.any-string}"),
                        Map.entry("accountId", "\${json-unit.any-string}")
                )
        )

        Map<String, Object> nestMap = Map.ofEntries(
                Map.entry("accountName", "モックギンコウコウザ1"),
                Map.entry("accountStatus", active),
                Map.entry("reasonCode", "0x****************************************************************"),
                Map.entry("balance", "1000"),
                Map.entry("appliedAt", **********),
                Map.entry("registeredAt", **********),
                Map.entry("terminatingAt", **********),
                Map.entry("terminatedAt", **********)
        )

        Map<String, Object> responseArgs = Map.ofEntries(
                Map.entry("accountData", nestMap),
                Map.entry("err", "")
        )

        Map<String, Object> responseMap = Map.ofEntries(
                Map.entry("data", responseArgs)
        )

        def responseData = objectMapper.writeValueAsString(responseMap)

        wiremockServer.stubFor(post(urlPathEqualTo("/call"))
                .withRequestBody(equalToJson(requestCheckData, true, true))
                .willReturn(okJson(responseData)))
    }

    static void mockCallValidatorGetAccountError(WireMockServer wiremockServer, String err) {
        // Mockのレスポンスを定義
        Map<String, Object> responseArgs = Map.ofEntries(
                Map.entry("err", err)
        )
        Map<String, Object> responseMap = Map.ofEntries(
                Map.entry("data", responseArgs)
        )
        def responseData = objectMapper.writeValueAsString(responseMap)

        // Mockのチェック用のリクエストを定義
        def requestCheckData = initBCClientRequest("\${json-unit.any-string}", "Validator", "getAccount",
                Map.ofEntries(
                        Map.entry("validatorId", "\${json-unit.any-string}"),
                        Map.entry("accountId", "\${json-unit.any-string}")
                )
        )

        wiremockServer.stubFor(post(urlPathEqualTo("/call"))
                .withRequestBody(equalToJson(requestCheckData, true, true))
                .willReturn(okJson(responseData)))
    }

    def "testAcceptable_ForceBurnBurnイベントでvalidatorIdがclient entityテーブルに存在しない場合"() {
        setup:

        JsonNode indexValues = getForceBurnNonIndexValues()
        String illegalValidatorId = """
            {
                "validatorId": [9,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "traceId": [55,55,55,55,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId":"3000",
                "burnedAmount": "0",
                "burnedBalance": "0",
                "forceDischarge": []
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(illegalValidatorId)

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_ForceBurnイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()
        JsonNode nonIndexValues = getForceBurnNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.FORCE_BURN
        result.validatorIds().size() == 1
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
    }

    def "testAcceptable_処理対象以外のイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()
        JsonNode nonIndexValues = getForceBurnNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_validatorIdがValidatorFilterのcacheに存在する場合"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()
        JsonNode nonIndexValues = getForceBurnNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()
        this.transactionTracker.acceptable(testEvent)

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.FORCE_BURN
        result.validatorIds().size() == 1
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
    }

    def "testOnMessage_ForceBurnイベントが発生した場合 - FZ残高あり - BZ口座あり - BZ残高あり"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()

        JsonNode nonIndexValues = getForceBurnNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ForceBurnEvent forceBurnEvent = ForceBurnEvent.create(testEvent)
        TransactionForceBurnMessage forceBurnMessage = TransactionForceBurnMessage.create(forceBurnEvent)

        mockCallValidatorGetAccount(wiremockServer)
        sql.execute("""
            INSERT INTO transaction (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type, amount,
                                     post_balance, other_account_id, other_account_name, other_zone_id)
            VALUES ('********-0000-0000-0000-************','0x0000001','60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb','アカウント名1','4',3000,'2021-01-01T00:00:00+09:00','mint',5000,5000,NULL,NULL,NULL),
            ('********-0000-0000-0000-********0002','0x0000002','60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb','アカウント名1','4',3000,'2021-01-02T00:00:00+09:00','burn',4000,1000,NULL,NULL,NULL),
            ('********-0000-0000-0000-************','0x0000003','60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb','アカウント名1','4',3000,'2021-01-03T00:00:00+09:00','mint',4000,5000,NULL,NULL,NULL);
        """)

        when:
        def result = this.transactionTracker.onMessage(forceBurnMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20';
        """)

        def memoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        def dischargeRecord_3001_FIN = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3000 AND other_zone_id = 3001;
        """)

        def dischargeRecord_3001_BIZ = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3001 AND other_zone_id = 3000;
        """)

        def dischargeRecord_3002_FIN = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3000 AND other_zone_id = 3002;
        """)

        def dischargeRecord_3002_BIZ = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3002 AND other_zone_id = 3000;
        """)

        def forceBurnRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_burn';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 5
        memoRecord.size() == 0
        miscRecord.size() == 0
        senderRecord.size() == 0

        // 強制ディスチャージ_3001
        dischargeRecord_3001_FIN.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        dischargeRecord_3001_FIN.get("account_name").equals("モックギンコウコウザ1")
        dischargeRecord_3001_FIN.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        dischargeRecord_3001_FIN.get("zone_id") == 3000
        !Objects.isNull(dischargeRecord_3001_FIN.get("transacted_at"))
        dischargeRecord_3001_FIN.get("transaction_type").equals("force_discharge")
        dischargeRecord_3001_FIN.get("amount") == 1000
        dischargeRecord_3001_FIN.get("post_balance") == 6000
        dischargeRecord_3001_FIN.get("other_account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(dischargeRecord_3001_FIN.get("other_account_name"))
        dischargeRecord_3001_FIN.get("other_zone_id") == 3001

        dischargeRecord_3001_BIZ.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        dischargeRecord_3001_BIZ.get("account_name").equals("モックギンコウコウザ1")
        dischargeRecord_3001_BIZ.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        dischargeRecord_3001_BIZ.get("zone_id") == 3001
        !Objects.isNull(dischargeRecord_3001_BIZ.get("transacted_at"))
        dischargeRecord_3001_BIZ.get("transaction_type").equals("force_discharge")
        dischargeRecord_3001_BIZ.get("amount") == -1000
        dischargeRecord_3001_BIZ.get("post_balance") == 0
        dischargeRecord_3001_BIZ.get("other_account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(dischargeRecord_3001_BIZ.get("other_account_name"))
        dischargeRecord_3001_BIZ.get("other_zone_id") == 3000

        // 強制ディスチャージ_3002
        dischargeRecord_3002_FIN.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        dischargeRecord_3002_FIN.get("account_name").equals("モックギンコウコウザ1")
        dischargeRecord_3002_FIN.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        dischargeRecord_3002_FIN.get("zone_id") == 3000
        !Objects.isNull(dischargeRecord_3002_FIN.get("transacted_at"))
        dischargeRecord_3002_FIN.get("transaction_type").equals("force_discharge")
        dischargeRecord_3002_FIN.get("amount") == 2000
        dischargeRecord_3002_FIN.get("post_balance") == 8000
        dischargeRecord_3002_FIN.get("other_account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(dischargeRecord_3002_FIN.get("other_account_name"))
        dischargeRecord_3002_FIN.get("other_zone_id") == 3002

        dischargeRecord_3002_BIZ.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        dischargeRecord_3002_BIZ.get("account_name").equals("モックギンコウコウザ1")
        dischargeRecord_3002_BIZ.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        dischargeRecord_3002_BIZ.get("zone_id") == 3002
        !Objects.isNull(dischargeRecord_3002_BIZ.get("transacted_at"))
        dischargeRecord_3002_BIZ.get("transaction_type").equals("force_discharge")
        dischargeRecord_3002_BIZ.get("amount") == -2000
        dischargeRecord_3002_BIZ.get("post_balance") == 0
        dischargeRecord_3002_BIZ.get("other_account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(dischargeRecord_3002_BIZ.get("other_account_name"))
        dischargeRecord_3002_BIZ.get("other_zone_id") == 3000

        // 強制償却
        forceBurnRecord.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        forceBurnRecord.get("account_name").equals("モックギンコウコウザ1")
        forceBurnRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        forceBurnRecord.get("zone_id") == 3000
        !Objects.isNull(forceBurnRecord.get("transacted_at"))
        forceBurnRecord.get("transaction_type").equals("force_burn")
        forceBurnRecord.get("amount") == -8000
        forceBurnRecord.get("post_balance") == 0
        Objects.isNull(forceBurnRecord.get("other_account_id"))
        Objects.isNull(forceBurnRecord.get("other_account_name"))
        Objects.isNull(forceBurnRecord.get("other_zone_id"))

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """
        )
    }

    def "testOnMessage_ForceBurnイベントが発生した場合 - BZ口座あり - BZ残高なし"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()
        JsonNode nonIndexValues = getForceBurnNonIndexValuesWithoutDischarge()

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ForceBurnEvent forceBurnEvent = ForceBurnEvent.create(testEvent)
        TransactionForceBurnMessage forceBurnMessage = TransactionForceBurnMessage.create(forceBurnEvent)

        mockCallValidatorGetAccount(wiremockServer)

        when:
        def result = this.transactionTracker.onMessage(forceBurnMessage)

        def dischargeRecords = sql.rows("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge';
        """)

        def forceBurnRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_burn';
        """)

        def memoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        dischargeRecords.size() == 0
        memoRecord.size() == 0
        miscRecord.size() == 0
        senderRecord.size() == 0

        // 強制償却
        forceBurnRecord.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        forceBurnRecord.get("account_name").equals("モックギンコウコウザ1")
        forceBurnRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        forceBurnRecord.get("zone_id") == 3000
        !Objects.isNull(forceBurnRecord.get("transacted_at"))
        forceBurnRecord.get("transaction_type").equals("force_burn")
        forceBurnRecord.get("amount") == -1000
        forceBurnRecord.get("post_balance") == 0
        Objects.isNull(forceBurnRecord.get("other_account_id"))
        Objects.isNull(forceBurnRecord.get("other_account_name"))
        Objects.isNull(forceBurnRecord.get("other_zone_id"))

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """
        )
    }

    def "testOnMessage_ForceBurnイベントが発生した場合 - BZ口座なし"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()
        String nonIndexValuesStr = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "traceId": [55,55,55,55,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId":"3000",
                "burnedAmount": "2000",
                "burnedBalance": "0",
                "forceDischarge": []
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ForceBurnEvent forceBurnEvent = ForceBurnEvent.create(testEvent)
        TransactionForceBurnMessage forceBurnMessage = TransactionForceBurnMessage.create(forceBurnEvent)

        mockCallValidatorGetAccount(wiremockServer)

        when:
        def result = this.transactionTracker.onMessage(forceBurnMessage)

        def dischargeRecords = sql.rows("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge';
        """)

        def forceBurnRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_burn';
        """)

        def memoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        dischargeRecords.size() == 0
        memoRecord.size() == 0
        miscRecord.size() == 0
        senderRecord.size() == 0

        // 強制償却
        forceBurnRecord.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        forceBurnRecord.get("account_name").equals("モックギンコウコウザ1")
        forceBurnRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        forceBurnRecord.get("zone_id") == 3000
        !Objects.isNull(forceBurnRecord.get("transacted_at"))
        forceBurnRecord.get("transaction_type").equals("force_burn")
        forceBurnRecord.get("amount") == -2000
        forceBurnRecord.get("post_balance") == 0
        Objects.isNull(forceBurnRecord.get("other_account_id"))
        Objects.isNull(forceBurnRecord.get("other_account_name"))
        Objects.isNull(forceBurnRecord.get("other_zone_id"))

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """
        )
    }

    def "testOnMessage_ForceBurnイベントが発生した場合 - BZ口座あり - 3002のみ強制ディスチャージ"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()

        String nonIndexValuesStr = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "traceId": [55,55,55,55,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId":"3000",
                "burnedAmount": "7000",
                "burnedBalance": "0",
                "forceDischarge": [{"zoneId":"3001","dischargeAmount":"0"},{"zoneId":"3002","dischargeAmount":"2000"}]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ForceBurnEvent forceBurnEvent = ForceBurnEvent.create(testEvent)
        TransactionForceBurnMessage forceBurnMessage = TransactionForceBurnMessage.create(forceBurnEvent)

        mockCallValidatorGetAccount(wiremockServer)
        sql.execute("""
            INSERT INTO transaction (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type, amount,
                                     post_balance, other_account_id, other_account_name, other_zone_id)
            VALUES ('********-0000-0000-0000-************','0x0000001','60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb','アカウント名1','4',3000,'2021-01-01T00:00:00+09:00','mint',5000,5000,NULL,NULL,NULL),
            ('********-0000-0000-0000-********0002','0x0000002','60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb','アカウント名1','4',3000,'2021-01-02T00:00:00+09:00','burn',4000,1000,NULL,NULL,NULL),
            ('********-0000-0000-0000-************','0x0000003','60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb','アカウント名1','4',3000,'2021-01-03T00:00:00+09:00','mint',4000,5000,NULL,NULL,NULL);
        """)

        when:
        def result = this.transactionTracker.onMessage(forceBurnMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20';
        """)

        def memoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        def dischargeRecord_3001_FIN = sql.rows("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3000 AND other_zone_id = 3001;
        """)

        def dischargeRecord_3001_BIZ = sql.rows("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3001 AND other_zone_id = 3000;
        """)

        def dischargeRecord_3002_FIN = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3000 AND other_zone_id = 3002;
        """)

        def dischargeRecord_3002_BIZ = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge' AND zone_id = 3002 AND other_zone_id = 3000;
        """)

        def forceBurnRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_burn';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 3
        memoRecord.size() == 0
        miscRecord.size() == 0
        senderRecord.size() == 0

        // 強制ディスチャージ_3001
        dischargeRecord_3001_FIN.size() == 0
        dischargeRecord_3001_BIZ.size() == 0

        // 強制ディスチャージ_3002
        dischargeRecord_3002_FIN.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        dischargeRecord_3002_FIN.get("account_name").equals("モックギンコウコウザ1")
        dischargeRecord_3002_FIN.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        dischargeRecord_3002_FIN.get("zone_id") == 3000
        !Objects.isNull(dischargeRecord_3002_FIN.get("transacted_at"))
        dischargeRecord_3002_FIN.get("transaction_type").equals("force_discharge")
        dischargeRecord_3002_FIN.get("amount") == 2000
        dischargeRecord_3002_FIN.get("post_balance") == 7000
        dischargeRecord_3002_FIN.get("other_account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(dischargeRecord_3002_FIN.get("other_account_name"))
        dischargeRecord_3002_FIN.get("other_zone_id") == 3002

        dischargeRecord_3002_BIZ.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        dischargeRecord_3002_BIZ.get("account_name").equals("モックギンコウコウザ1")
        dischargeRecord_3002_BIZ.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        dischargeRecord_3002_BIZ.get("zone_id") == 3002
        !Objects.isNull(dischargeRecord_3002_BIZ.get("transacted_at"))
        dischargeRecord_3002_BIZ.get("transaction_type").equals("force_discharge")
        dischargeRecord_3002_BIZ.get("amount") == -2000
        dischargeRecord_3002_BIZ.get("post_balance") == 0
        dischargeRecord_3002_BIZ.get("other_account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        Objects.isNull(dischargeRecord_3002_BIZ.get("other_account_name"))
        dischargeRecord_3002_BIZ.get("other_zone_id") == 3000

        // 強制償却
        forceBurnRecord.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        forceBurnRecord.get("account_name").equals("モックギンコウコウザ1")
        forceBurnRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        forceBurnRecord.get("zone_id") == 3000
        !Objects.isNull(forceBurnRecord.get("transacted_at"))
        forceBurnRecord.get("transaction_type").equals("force_burn")
        forceBurnRecord.get("amount") == -7000
        forceBurnRecord.get("post_balance") == 0
        Objects.isNull(forceBurnRecord.get("other_account_id"))
        Objects.isNull(forceBurnRecord.get("other_account_name"))
        Objects.isNull(forceBurnRecord.get("other_zone_id"))

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """
        )
    }

    def "testOnMessage_ForceBurnイベントが発生した場合 - 償却額0 - BZ口座なし"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()
        String nonIndexValuesStr = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "traceId": [55,55,55,55,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId":"3000",
                "burnedAmount": "0",
                "burnedBalance": "0",
                "forceDischarge": []
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ForceBurnEvent forceBurnEvent = ForceBurnEvent.create(testEvent)
        TransactionForceBurnMessage forceBurnMessage = TransactionForceBurnMessage.create(forceBurnEvent)

        mockCallValidatorGetAccount(wiremockServer)

        when:
        def result = this.transactionTracker.onMessage(forceBurnMessage)

        def dischargeRecords = sql.rows("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge';
        """)

        def forceBurnRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_type = 'force_burn';
        """)

        def memoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        dischargeRecords.size() == 0
        forceBurnRecord.size() == 0
        memoRecord.size() == 0
        miscRecord.size() == 0
        senderRecord.size() == 0

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """
        )
    }


    def "testOnMessage_BCClientからエラーが返却された場合"() {
        setup:
        JsonNode indexValues = getForceBurnIndexValues()
        JsonNode nonIndexValues = getForceBurnNonIndexValuesWithoutDischarge()

        def testEvent = BCEvent.<Message> builder()
                .name("ForceBurn")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ForceBurnEvent forceBurnEvent = ForceBurnEvent.create(testEvent)
        TransactionForceBurnMessage forceBurnMessage = TransactionForceBurnMessage.create(forceBurnEvent)

        mockCallValidatorGetAccountError(wiremockServer, "6002:not exist")

        when:
        def result = this.transactionTracker.onMessage(forceBurnMessage)

        def dischargeRecords = sql.rows("""
            SELECT * FROM transaction WHERE transaction_type = 'force_discharge';
        """)

        def forceBurnRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_type = 'force_burn';
        """)

        def memoRecord = sql.rows("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.rows("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.rows("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        dischargeRecords.size() == 0
        memoRecord.size() == 0
        miscRecord.size() == 0
        senderRecord.size() == 0

        // 強制償却
        forceBurnRecord.get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        forceBurnRecord.get("account_name") == null //アカウント名がない
        forceBurnRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        forceBurnRecord.get("zone_id") == 3000
        !Objects.isNull(forceBurnRecord.get("transacted_at"))
        forceBurnRecord.get("transaction_type").equals("force_burn")
        forceBurnRecord.get("amount") == -1000
        forceBurnRecord.get("post_balance") == 0
        Objects.isNull(forceBurnRecord.get("other_account_id"))
        Objects.isNull(forceBurnRecord.get("other_account_name"))
        Objects.isNull(forceBurnRecord.get("other_zone_id"))

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """
        )
    }
}
