package com.decurret_dcp.dcjpy.bctracker.core.transaction.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneBalanceEvent
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionSyncBizZoneBalanceMessage
import com.decurret_dcp.dcjpy.bctracker.core.transaction.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest()
class TransactionTrackerForSyncBusinessZoneBalanceSpec extends Specification {

    @Autowired
    TransactionTracker transactionTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        // SQSのQueueを作成しないと、起動時にエラーになる。
        sqsClient = CoreAdhocHelper.createSQS("dcjpy_bctracker_queue_transaction.fifo")
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * SyncBusinessZoneBalanceイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return SyncBusinessZoneBalanceイベントのnonIndexValuesの値
     */
    private static JsonNode getIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * SyncBusinessZoneBalanceイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return SyncBusinessZoneBalanceイベントのindexValuesの値
     */
    private static JsonNode getNonIndexValues() {
        String nonIndexValues = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3001",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "100",
                    "toAccountBalance": "10",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,100],
                    "fromAccountName": "モックギンコウコウザ1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "モックギンコウコウザ2",
                    "amount": "1000",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "memo": "メモmemo"
                }
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    def "testAcceptable_SyncBusinessZoneBalanceイベントが発生しvalidatorIdがclient entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getIndexValues()
        String testData = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountBalance": "100",
                    "toAccountBalance": "10",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "モックギンコウコウザ1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "1000",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_SyncBusinessZoneBalanceイベントが発生した場合_#testCase"() {
        setup:
        JsonNode indexValues = getIndexValues()
        String testData = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId":  $fromValidatorId,
                    "toValidatorId": $toValidatorId,
                    "fromAccountBalance": "100",
                    "toAccountBalance": "10",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "モックギンコウコウザ1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "1000",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorIds().size() == 2
        result.validatorIds().get(0).getValue().equals(validatorId1)
        result.validatorIds().get(1).getValue().equals(validatorId2)
        result.eventType() == BCEventType.SYNC_BUSINESS_ZONE_BALANCE
        where:
        testCase                                                       | fromValidatorId                                                                                           | toValidatorId                                                                                             | validatorId1                       | validatorId2
        "fromValidatorIdがentityテーブルに存在する場合"                | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba" | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb"
        "toValidatorIdがentityテーブルに存在する場合"                  | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFbb" | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
        "fromValidatorIdとtoValidatorIdがentityテーブルに存在する場合" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba" | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
    }

    def "testOnMessage_SynBusinessZoneBalanceイベントが発生した場合_fromValidatorIdとtoValidatorIdがentityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getIndexValues()

        String testData = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "toValidatorId":  [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "fromAccountBalance": "100",
                    "toAccountBalance": "10",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "モックギンコウコウザ1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "1000",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "memo": "メモmemo"
                }
            }
        """

        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        SyncBusinessZoneBalanceEvent balanceEvent = SyncBusinessZoneBalanceEvent.create(testEvent)
        TransactionSyncBizZoneBalanceMessage message = TransactionSyncBizZoneBalanceMessage.create(balanceEvent)

        when:
        def result = this.transactionTracker.onMessage(message)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """
        )

        def memoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo;
        """
        )
        def miscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc;
        """)
        def senderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true

        Objects.isNull(fromAccountRecord)
        Objects.isNull(toAccountRecord)
        Objects.isNull(memoRecord)
        Objects.isNull(miscRecord)
        Objects.isNull(senderRecord)
    }

    def "testOnMessage_SynBusinessZoneBalanceイベントが発生した場合_toValidatorIdがentityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getIndexValues()
        JsonNode nonIndexValues = getNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        SyncBusinessZoneBalanceEvent balanceEvent = SyncBusinessZoneBalanceEvent.create(testEvent)
        TransactionSyncBizZoneBalanceMessage message = TransactionSyncBizZoneBalanceMessage.create(balanceEvent)

        when:
        def result = this.transactionTracker.onMessage(message)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """
        )

        def memoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo;
        """
        )
        def miscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc;
        """)
        def senderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true

        !Objects.isNull(fromAccountRecord)
        !Objects.isNull(fromAccountRecord.get("transaction_id"))
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd")
        fromAccountRecord.get("account_name").equals("モックギンコウコウザ1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -1000
        fromAccountRecord.get("post_balance") == 100
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("モックギンコウコウザ2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        Objects.isNull(toAccountRecord)

        Objects.isNull(memoRecord)
        Objects.isNull(miscRecord)
        Objects.isNull(senderRecord)

        cleanup:
        sql.execute("""
            DELETE FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd' AND zone_id = '3001';
        """)
    }

    def "testOnMessage_SynBusinessZoneBalanceイベントが発生した場合_fromValidatorIdがentityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getIndexValues()

        String testData = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3001",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "100",
                    "toAccountBalance": "10",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,100],
                    "fromAccountName": "モックギンコウコウザ1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "モックギンコウコウザ2",
                    "amount": "1000",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        SyncBusinessZoneBalanceEvent balanceEvent = SyncBusinessZoneBalanceEvent.create(testEvent)
        TransactionSyncBizZoneBalanceMessage message = TransactionSyncBizZoneBalanceMessage.create(balanceEvent)

        when:
        def result = this.transactionTracker.onMessage(message)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """
        )

        def memoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo;
        """
        )
        def miscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc;
        """)
        def senderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true

        Objects.isNull(fromAccountRecord)

        !Objects.isNull(toAccountRecord)
        !Objects.isNull(toAccountRecord.get("transaction_id"))
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("モックギンコウコウザ2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3001
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("transfer")
        toAccountRecord.get("amount") == 1000
        toAccountRecord.get("post_balance") == 10
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd")
        toAccountRecord.get("other_account_name").equals("モックギンコウコウザ1")
        Objects.isNull(toAccountRecord.get("other_zone_id"))

        Objects.isNull(memoRecord)
        Objects.isNull(miscRecord)
        Objects.isNull(senderRecord)

        cleanup:
        sql.execute("""
            DELETE FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """)
    }

    def "testOnMessage_SynBusinessZoneBalanceイベントが発生した場合_fromValidatorIdとvalidatorIdがentityテーブルに存在する場合"() {
        setup:
        JsonNode indexValues = getIndexValues()

        String testData = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3001",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId":  [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "100",
                    "toAccountBalance": "10",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,100],
                    "fromAccountName": "モックギンコウコウザ1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "モックギンコウコウザ2",
                    "amount": "1000",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "memo": "メモmemo"
                }
            }
        """

        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        SyncBusinessZoneBalanceEvent balanceEvent = SyncBusinessZoneBalanceEvent.create(testEvent)
        TransactionSyncBizZoneBalanceMessage message = TransactionSyncBizZoneBalanceMessage.create(balanceEvent)

        when:
        def result = this.transactionTracker.onMessage(message)
        def fromAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd' AND zone_id = '3001';
        """)

        def toAccountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
        """
        )

        def memoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo;
        """
        )
        def miscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc;
        """)
        def senderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true

        !Objects.isNull(fromAccountRecord)
        !Objects.isNull(fromAccountRecord.get("transaction_id"))
        fromAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        fromAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd")
        fromAccountRecord.get("account_name").equals("モックギンコウコウザ1")
        fromAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        fromAccountRecord.get("zone_id") == 3001
        !Objects.isNull(fromAccountRecord.get("transacted_at"))
        fromAccountRecord.get("transaction_type").equals("transfer")
        fromAccountRecord.get("amount") == -1000
        fromAccountRecord.get("post_balance") == 100
        fromAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        fromAccountRecord.get("other_account_name").equals("モックギンコウコウザ2")
        Objects.isNull(fromAccountRecord.get("other_zone_id"))

        !Objects.isNull(toAccountRecord)
        !Objects.isNull(toAccountRecord.get("transaction_id"))
        toAccountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        toAccountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        toAccountRecord.get("account_name").equals("モックギンコウコウザ2")
        toAccountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        toAccountRecord.get("zone_id") == 3001
        !Objects.isNull(toAccountRecord.get("transacted_at"))
        toAccountRecord.get("transaction_type").equals("transfer")
        toAccountRecord.get("amount") == 1000
        toAccountRecord.get("post_balance") == 10
        toAccountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd")
        toAccountRecord.get("other_account_name").equals("モックギンコウコウザ1")
        Objects.isNull(toAccountRecord.get("other_zone_id"))

        Objects.isNull(memoRecord)
        Objects.isNull(miscRecord)
        Objects.isNull(senderRecord)

        cleanup:
        sql.execute("""
            DELETE FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc' AND zone_id = '3001';
            DELETE FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20' AND account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbd' AND zone_id = '3001';
        """)
    }

}
