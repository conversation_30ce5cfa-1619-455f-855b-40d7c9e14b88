package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.ValidatorApi;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.command.ValidatorGetAccountCommand;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.ClientEntityRepository;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.TransactionRepository;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.service.ValidatorFilter;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionBurnMessage;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntities;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionForceBurnMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionIssueVoucherMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionMintMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionRedeemVoucherMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionSyncBizZoneBalanceMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionTransferMessage;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@Service
@Slf4j
@Transactional
public class RegisterTransaction {

    private final ValidatorFilter validatorFilter;

    private final TransactionRepository transactionRepository;

    private final ClientEntityRepository clientEntityRepository;

    private final ValidatorApi validatorApi;

    private record ValidatorExists(boolean fromExist, boolean toExist) {
        // Do other codes.
    }

    /**
     * DCJPY 発行に関する取引履歴を登録する。
     *
     * @param message DCJPY 発行メッセージ
     */
    public void registerTransaction(TransactionMintMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * DCJPY 償却に関する取引履歴を登録する。
     *
     * @param message DCJPY 償却メッセージ
     */
    public void registerTransaction(TransactionBurnMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * Transfer イベントに関する取引履歴を登録する。
     *
     * @param message Transfer イベントメッセージ
     */
    public void registerTransaction(TransactionTransferMessage message) {
        switch (message.transactionType()) {
            case TRANSFER -> this.registerTransactionForTransfer(message);
            case CHARGE -> this.registerTransactionForCharge(message);
            case DISCHARGE -> this.registerTransactionForDischarge(message);
        }
    }

    /**
     * DCJPY 移転に関する取引履歴を登録する。(自身のDLT)
     *
     * @param message DCJPY 移転メッセージ
     */
    void registerTransactionForTransfer(TransactionTransferMessage message) {
        ValidatorExists validatorExists = this.existsValidators(message.fromValidatorId, message.toValidatorId);
        TransactionEntities fromTransactions = message.createTransferTransactionEntitiesFromAccount();
        TransactionEntities toTransactions = message.createTransferTransactionEntitiesToAccount();

        // from取引履歴テーブル登録
        if (validatorExists.fromExist) {
            this.transactionRepository.resisterTransferTransaction(fromTransactions);
            logTransaction(fromTransactions.transaction, "from ");
        }

        if (validatorExists.toExist) {
            // to取引履歴テーブル登録
            this.transactionRepository.resisterTransferTransaction(toTransactions);
            logTransaction(toTransactions.transaction, "to ");
        }
    }

    private ValidatorExists existsValidators(ValidatorId from, ValidatorId to) {
        boolean fromExist = this.validatorFilter.acceptable(from);
        boolean toExist = this.validatorFilter.acceptable(to);

        return new ValidatorExists(fromExist, toExist);
    }

    /**
     * FinZone にて、DCJPY チャージに関する取引履歴を登録する。
     *
     * @param message DCJPY チャージメッセージ
     */
    void registerTransactionForCharge(TransactionTransferMessage message) {
        TransactionEntity fromTransaction = message.createChargeTransactionFromAccount();
        TransactionEntity toTransaction = message.createChargeTransactionToAccount();

        // from取引履歴テーブル登録
        this.transactionRepository.registerTransaction(fromTransaction);
        // to取引履歴テーブル登録
        this.transactionRepository.registerTransaction(toTransaction);

        logTransaction(fromTransaction, "from ");
        logTransaction(toTransaction, "to ");
    }

    /**
     * FinZone にて、DCJPY ディスチャージに関する取引履歴を登録する。
     *
     * @param message DCJPY ディスチャージメッセージ
     */
    void registerTransactionForDischarge(TransactionTransferMessage message) {
        TransactionEntity fromTransaction = message.createDisChargeTransactionFromAccount();
        TransactionEntity toTransaction = message.createDisChargeTransactionToAccount();

        // from取引履歴テーブル登録
        this.transactionRepository.registerTransaction(fromTransaction);
        // to取引履歴テーブル登録
        this.transactionRepository.registerTransaction(toTransaction);

        logTransaction(fromTransaction, "from ");
        logTransaction(toTransaction, "to ");
    }

    /**
     * BizZone にて、DCJPY チャージ (BZ発行) に関する取引履歴を登録する。
     *
     * @param message DCJPY BZ 発行メッセージ
     */
    public void registerTransaction(TransactionIssueVoucherMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * BizZone にて、DCJPY ディスチャージ (BZ償却) に関する取引履歴を登録する。
     *
     * @param message DCJPY BZ 償却メッセージ
     */
    public void registerTransaction(TransactionRedeemVoucherMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * FinZone にて、BizZone の DCJPY 移転に関する取引履歴を登録する。
     *
     * @param message DCJPY 残高同期メッセージ
     */
    public void registerTransaction(TransactionSyncBizZoneBalanceMessage message) {
        ValidatorExists validatorExists = this.existsValidators(message.fromValidatorId, message.toValidatorId);
        TransactionEntity fromTransaction = message.createSyncBizBalanceTransactionFromAccount();
        TransactionEntity toTransaction = message.createSyncBizBalanceTransactionToAccount();

        // from取引履歴テーブル登録
        if (validatorExists.fromExist) {
            this.transactionRepository.registerTransaction(fromTransaction);
            logTransaction(fromTransaction, "from ");
        }

        // to取引履歴テーブル登録
        if (validatorExists.toExist) {
            this.transactionRepository.registerTransaction(toTransaction);
            logTransaction(toTransaction, "to ");
        }
    }

    /**
     * FinZone にて、強制償却に関する取引履歴を登録する。
     *
     * @param message DCJPY 残高同期メッセージ
     */
    public void registerTransaction(TransactionForceBurnMessage message) {

        // バリデータのゾーンを取得する
        ZoneId zoneId = this.clientEntityRepository.findZoneIdByEntityId(message.validatorId.toEntityId());

        // アカウント名を取得
        ValidatorGetAccountCommand command = ValidatorGetAccountCommand.builder()
                .zoneId(zoneId)
                .validatorId(message.validatorId)
                .accountId(message.accountId)
                .build();
        Either<ValidatorGetAccountResult> either = this.validatorApi.getAccount(command);
        if (either.isSuccess() == false) {
            log.error("Failed to get account. command : {}, response {}", command, either.failure());
        }

        ValidatorGetAccountResult accountResult = either.isSuccess() ? either.success() : null;
        List<TransactionEntity> transactions = message.toTransactions(accountResult);

        transactions.forEach(entity -> {
            this.transactionRepository.registerTransaction(entity);
            logTransaction(entity);
        });
    }

    private static void logTransaction(TransactionEntity transaction) {
        logTransaction(transaction, "");
    }

    private static void logTransaction(TransactionEntity transaction, String transactionOption) {
        log.info("Save {}transaction. transactionHash : {}, "
                         + "accountId : {}, zoneId : {}, transactionType : {}, amount : {}",
                 transactionOption, transaction.transactionHash.getValue(), transaction.accountId.getValue(),
                 transaction.zoneId.getValue(), transaction.transactionType.getValue(), transaction.amount.getValue());
    }
}
