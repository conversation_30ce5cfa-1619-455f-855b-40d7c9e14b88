package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value;

import java.util.Collections;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.BurnEvent;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 償却時の取引履歴を保存するためのメッセージ
 */
@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class TransactionBurnMessage implements TransactionMessage {

    /** トランザクションハッシュ. */
    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimestamp;

    public final ZoneId zoneId;

    public final ValidatorId validatorId;

    /** アカウントID. */
    public final AccountId accountId;

    public final String accountName;

    public final Amount amount;

    public final Balance balance;

    @Override
    public List<ValidatorId> validatorIds() {
        return Collections.singletonList(this.validatorId);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.BURN;
    }

    public static TransactionBurnMessage create(BurnEvent event) {
        return TransactionBurnMessage.builder()
                .transactionHash(event.transactionHash)
                .blockTimestamp(event.blockTimeStamp)
                .zoneId(event.zoneId)
                .validatorId(event.validatorId)
                .accountId(event.accountId)
                .accountName(event.accountName)
                .amount(event.amount)
                .balance(event.balance)
                .build();
    }

    public TransactionEntity toTransaction() {
        return TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.accountId)
                .accountName(this.accountName)
                .validatorId(this.validatorId)
                .zoneId(this.zoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.BURN)
                .amount(Amount.of(this.amount.getValue().negate()))
                .postBalance(this.balance)
                .build();
    }

}
