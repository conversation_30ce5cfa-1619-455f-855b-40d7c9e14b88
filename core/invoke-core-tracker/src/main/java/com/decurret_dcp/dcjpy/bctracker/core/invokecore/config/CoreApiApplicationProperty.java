package com.decurret_dcp.dcjpy.bctracker.core.invokecore.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ConfigurationProperties(prefix = "bctracker.invoke-core")
@ConstructorBinding
@AllArgsConstructor
@ToString
public class CoreApiApplicationProperty {

    /** AWS 環境に接続する場合の 各 local-endpoint の設定値。 */
    private static final String MODE_CONNECT_TO_AWS = "-";

    /** CoreAPI に関する設定。 */
    public final CoreApiProperty coreApi;

    /** SecretManager に関する設定。 */
    public final SecretManagerProperty secretManager;

    /** ログ出力時のリクエスト及びレスポンスのボディの最大長。 */
    public final Integer loggingMaxSize;

    @AllArgsConstructor
    @ToString
    public static class CoreApiProperty {

        /** Core API の URL. */
        public final String baseUrl;

        /** Core で必要な access_token 取得先の認証用 URL. */
        public final String idpUrl;

        /** Core からのレスポンスのタイムアウト値(ミリ秒)。 */
        public final Long readTimeoutMillisecond;

        /** HTTP コネクションの設定。 */
        public final HttpConnectionProperty httpConnection;

        @AllArgsConstructor
        @ToString
        public static class HttpConnectionProperty {

            public final Integer maxPerRoute;

            public final Integer maxTotal;
        }
    }

    @AllArgsConstructor
    @ToString
    @EqualsAndHashCode
    public static class SecretManagerProperty {

        /** SecretManager の接続先をローカル環境に設定するための設定(AWS環境に接続する場合は '-' を設定する)。 */
        public final String localEndpoint;

        /**
         * AWS 環境の SNS に接続するかどうか。
         *
         * @return AWS 環境に接続する場合 true
         */
        public boolean toAwsConnection() {
            return (StringUtils.hasText(this.localEndpoint) == false) || MODE_CONNECT_TO_AWS.equals(this.localEndpoint);
        }
    }
}
