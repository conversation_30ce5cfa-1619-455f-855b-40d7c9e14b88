package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi;

import org.springframework.http.client.ClientHttpRequestInterceptor;

import com.decurret_dcp.dcjpy.bctracker.base.domain.service.TraceIdHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TraceId;

public class TraceIdInterceptor {

    public static ClientHttpRequestInterceptor get() {
        return (request, body, execution) -> {
            TraceId traceId = TraceIdHolder.getTraceId();
            if (traceId != null) {
                request.getHeaders().set("trace-id", traceId.getValue());
            }

            return execution.execute(request, body);
        };
    }
}
