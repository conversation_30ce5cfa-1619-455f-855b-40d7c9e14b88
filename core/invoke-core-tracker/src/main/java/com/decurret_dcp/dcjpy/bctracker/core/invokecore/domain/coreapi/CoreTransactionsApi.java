package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.command.CoreApiDischargeCommand;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.result.CoreApiDischargeResult;

/**
 * Core APIを呼び出すI/F
 */
public interface CoreTransactionsApi {

    /**
     * Core API 31-54 FinZone向け BizZoneコインのディスチャージ
     *
     * @param command ディスチャージコマンド
     *
     * @return ディスチャージ結果
     */
    public Either<CoreApiDischargeResult> discharge(CoreApiDischargeCommand command);
}
