package com.decurret_dcp.dcjpy.bctracker.core.invokecore;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import com.decurret_dcp.dcjpy.bctracker.base.config.BCTrackerBaseSqsProperty;
import com.decurret_dcp.dcjpy.bctracker.core.share.config.BCClientProperty;

/**
 * CoreAPI実行 (CORE)
 */
@SpringBootApplication
@ComponentScan("com.decurret_dcp.dcjpy.bctracker")
@EnableConfigurationProperties({ BCTrackerBaseSqsProperty.class, BCClientProperty.class})
public class InvokeCoreTrackerMain {
    public static void main(String[] args) {
        SpringApplication.run(InvokeCoreTrackerMain.class, args);
    }
}
