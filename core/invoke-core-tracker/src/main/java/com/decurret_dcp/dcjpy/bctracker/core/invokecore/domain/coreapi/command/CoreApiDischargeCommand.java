package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.command;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class CoreApiDischargeCommand {

    public final RequestId requestId;

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final Amount dischargeAmount;
}
