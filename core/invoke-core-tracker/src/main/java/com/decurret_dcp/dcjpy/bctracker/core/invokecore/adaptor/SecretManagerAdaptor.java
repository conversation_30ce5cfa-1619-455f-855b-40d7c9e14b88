package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityId;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception.ResourceNotFoundException;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.service.CredentialStorage;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.credential.CredentialEntity;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.credential.Oauth2Secret;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;

@RequiredArgsConstructor
@Component
@Slf4j
public class SecretManagerAdaptor implements CredentialStorage {

    private final SecretsManagerClient client;

    private final ObjectMapper objectMapper;

    // ClientSecretを取得するためのIDを生成
    private static String authorityClientSecret(EntityId entityId) {
        return "dcjpy/core/client_credentials/" + entityId.getValue();
    }

    @Override
    public Oauth2Secret findClientSecret(EntityId entityId) {
        String secretId = authorityClientSecret(entityId);
        return this.doFindCredentials(secretId, Oauth2Secret.class);
    }

    private <TYPE extends CredentialEntity> TYPE doFindCredentials(String secretId, Class<TYPE> returnType) {
        GetSecretValueResponse response = this.doFindSecretValue(secretId);

        try {
            return this.objectMapper.readValue(response.secretString(), returnType);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException("Failed to decode credential. secretId = " + secretId, jsonExc);
        }
    }

    private GetSecretValueResponse doFindSecretValue(String secretId) throws ResourceNotFoundException {
        GetSecretValueRequest request = GetSecretValueRequest.builder()
                .secretId(secretId)
                .build();

        try {
            return this.client.getSecretValue(request);
        } catch (software.amazon.awssdk.services.secretsmanager.model.ResourceNotFoundException notFound) {
            throw new ResourceNotFoundException(
                    "Credential is not found in secretManager. secretId = " + secretId, notFound);
        }
    }
}
