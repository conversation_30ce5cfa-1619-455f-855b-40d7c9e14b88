package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception;

public class ResourceNotFoundException extends RuntimeException {

    public ResourceNotFoundException(String message) {
        super(message);
    }

    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public ResourceNotFoundException(RuntimeException exc) {
        super(exc.getMessage(), exc);
    }
}
