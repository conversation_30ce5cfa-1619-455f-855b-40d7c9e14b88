package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value;

import java.util.Collections;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TraceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.DischargeRequestedEvent;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.command.CoreApiDischargeCommand;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * CoreのディスチャージAPIを実行するためのメッセージ
 */
@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class DischargeRequestedMessage implements InvokeCoreMessage{

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final ZoneId fromZoneId;

    public final Amount amount;

    public final TraceId traceId;

    @Override
    public List<ValidatorId> validatorIds() {
        return Collections.singletonList(this.validatorId);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.DISCHARGE_REQUESTED;
    }

    public static DischargeRequestedMessage create(DischargeRequestedEvent event) {
        return DischargeRequestedMessage.builder()
                .validatorId(event.validatorId)
                .accountId(event.accountId)
                .fromZoneId(event.fromZoneId)
                .amount(event.amount)
                .traceId(event.traceId)
                .build();
    }

    public CoreApiDischargeCommand toCommand() {
        return CoreApiDischargeCommand.builder()
                .validatorId(this.validatorId)
                .requestId(RequestId.create())
                .accountId(this.accountId)
                .dischargeAmount(this.amount)
                .build();
    }
}
