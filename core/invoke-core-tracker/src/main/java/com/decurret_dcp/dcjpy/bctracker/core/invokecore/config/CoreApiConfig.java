package com.decurret_dcp.dcjpy.bctracker.core.invokecore.config;

import java.net.URI;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.RestTemplateLoggingInterceptor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClientBuilder;

@Configuration
@RequiredArgsConstructor
@Slf4j
@EnableConfigurationProperties(CoreApiApplicationProperty.class)
public class CoreApiConfig {

    @Bean
    public SecretsManagerClient secretsManagerClient(CoreApiApplicationProperty property) {
        CoreApiApplicationProperty.SecretManagerProperty smProperty = property.secretManager;

        SecretsManagerClientBuilder builder = SecretsManagerClient.builder();

        if (smProperty.toAwsConnection() == false) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .endpointOverride(URI.create(smProperty.localEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));

            log.error(
                    "Change the SecretManager connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    smProperty.localEndpoint
            );
        }

        return builder.build();
    }

    @Bean
    public RestTemplateCustomizer restTemplateCustomizer(CoreApiApplicationProperty property) {
        final int loggingMaxSize = property.loggingMaxSize != null ? property.loggingMaxSize.intValue() : 0;
        return (restTemplate) -> restTemplate.getInterceptors().add(new RestTemplateLoggingInterceptor(loggingMaxSize));
    }

}
