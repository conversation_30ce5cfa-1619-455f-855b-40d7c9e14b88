package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value;

import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.DischargeRequestedEvent;

public interface InvokeCoreMessage {

    public List<ValidatorId> validatorIds();

    public BCEventType eventType();

    public static InvokeCoreMessage create(BCEventTypeHolder event) {
        return switch (event.eventType()) {
            case DISCHARGE_REQUESTED -> DischargeRequestedMessage.create((DischargeRequestedEvent) event);
            default -> null;
        };
    }
}
