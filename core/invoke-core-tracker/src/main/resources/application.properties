# SpringBoot ã®ããã¼ãè¡¨ç¤ºããªãããã«ãã
spring.main.banner-mode=off

# ã­ã°ã®ãã©ã¼ããã
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(\\(%32.32X{traceId}\\)){magenta} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx

##########################################################################
# DB
##########################################################################
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=jdbc:postgresql://${DB_BASE:localhost}:${DB_PORT:5432}/${DB_NAME:postgres}
spring.datasource.username=${DB_USER:postgres}
spring.datasource.password=${DB_PASS:postgres}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.maximum-pool-size=${DB_MAXIMUM_POOL_SIZE:50}
spring.datasource.hikari.minimum-idle=${DB_MINIMUM_IDLE:10}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}

##########################################################################
# JACKSON
##########################################################################
# JSON ã®ãã©ã¡ã¼ã¿åã snake_case ã¨ãã¦æ±ã
spring.jackson.property-naming-strategy=SNAKE_CASE
# JSON ã®æ¥æå½¢å¼ãæ¨æºå½¢å¼ã¨ãã¦æ±ã
spring.jackson.serialization.write-dates-as-timestamps=false

##########################################################################
# BCTracker å±éæ©è½ (SQS)
##########################################################################
# æ±ãã¤ãã³ããæ ¼ç´ãããã­ã¥ã¼åã
bctracker.base.sqs.queue-name=${PUSH_NOTIFICATION_SQS_QUEUE_NAME:dcjpy_bctracker_queue_invoke-core.fifo}
# å¯è¦æ§ã¿ã¤ã ã¢ã¦ãæé (ç§)
bctracker.base.sqs.visibility-timeout=${BASE_VISIBILITY_TIMEOUT:5}
# ãã¼ãªã³ã°ã¡ãã»ã¼ã¸å¾æ©æé (ç§)
bctracker.base.sqs.wait-time-seconds=${BASE_WAIT_TIME_SECONDS:1}
# ã­ã¼ã«ã«ç°å¢ã¸ã®æ¥ç¶å
bctracker.base.sqs.local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:14566}

##########################################################################
# BCClientã®è¨­å®
##########################################################################
bctracker.base.bcclient.base-url=${BCCLIENT_APP_BASE_URL:http://localhost:8081}
# BCã¯ã©ã¤ã¢ã³ãã«å¯¾ããHTTPã³ãã¯ã·ã§ã³æ°ã®æå¤§å¤
bctracker.base.bcclient.http-connection-max-per-route=${BCCLIENT_HTTP_CONNECTION_MAX_PER_ROUTE:50}
# å¨HTTPã³ãã¯ã·ã§ã³æ°ã®æå¤§å¤ï¼ç¾ç¶ã¯ã³ãã¯ã·ã§ã³åãBCã¯ã©ã¤ã¢ã³ãããå­å¨ããªãããHTTP_CONNECTION_MAX_PER_ROUTEã¨åå¤ãè¨­å®ï¼
bctracker.base.bcclient.http-connection-max-total=${BCCLIENT_HTTP_CONNECTION_MAX_TOTAL:50}
# BCã¯ã©ã¤ã¢ã³ãããã®ã¬ã¹ãã³ã¹ã®ã¿ã¤ã ã¢ã¦ãå¤(ããªç§)
bctracker.base.bcclient.read-timeout-millisecond=${BCCLIENT_READ_TIMEOUT_MILLISEC:20000}

##########################################################################
# Core API
##########################################################################
# Core API ã® URL
bctracker.invoke-core.core-api.base-url=${CORE_APP_BASE_URL:http://localhost:8080}
# Core ã§å¿è¦ãª access_token åå¾åã®èªè¨¼ç¨ URL
bctracker.invoke-core.core-api.idp-url=${CORE_COGNITO_TOKEN_URL:http://localhost:18182/oauth2/token}
# Core ããã®ã¬ã¹ãã³ã¹ã®ã¿ã¤ã ã¢ã¦ãå¤(ããªç§)
bctracker.invoke-core.core-api.read-timeout-millisecond=${CORE_READ_TIMEOUT_MILLISEC:50000}

##########################################################################
# Http Connection
##########################################################################
# Coreã«å¯¾ããHTTPã³ãã¯ã·ã§ã³æ°ã®æå¤§å¤
bctracker.invoke-core.core-api.http-connection.max-per-route=${CORE_HTTP_CONNECTION_MAX_PER_ROUTE:100}
# å¨HTTPã³ãã¯ã·ã§ã³æ°ã®æå¤§å¤
bctracker.invoke-core.core-api.http-connection.max-total=${CORE_HTTP_CONNECTION_MAX_TOTAL:100}

##########################################################################
# Secret Manager
##########################################################################
# Secret Manager ã®æ¥ç¶åãã­ã¼ã«ã«ç°å¢ã«è¨­å®ããããã®è¨­å®(AWSç°å¢ã«æ¥ç¶ããå ´åã¯ç©ºæå­ãè¨­å®ãã)
bctracker.invoke-core.secret-manager.local-endpoint=${CORE_SECRET_MANAGER_LOCAL_ENDPOINT:http://localhost:14566}