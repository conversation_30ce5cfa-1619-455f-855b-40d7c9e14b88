package com.decurret_dcp.dcjpy.bctracker.core.balance.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.ToString;

@ConstructorBinding
@ConfigurationProperties(prefix = "bctracker.balance")
@AllArgsConstructor
@ToString
public class BalanceTrackerProperty {

    /** DynamoDb のローカル環境への接続先。 */
    public final String dynamodbLocalEndpoint;

    /** DynamoDb の接続先リージョン。 */
    public final String dynamodbRegion;

    /** 残高キャッシュのテーブル名。 */
    public final String balanceCacheTable;

    /**
     * AWS 環境の DynamoDb に接続するかどうか。
     *
     * @return AWS 環境の DynamoDb に接続する場合 true
     */
    public boolean toAwsConnection() {
        return (StringUtils.hasText(this.dynamodbLocalEndpoint) == false);
    }
}
