package com.decurret_dcp.dcjpy.bctracker.core.balance.application;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bctracker.base.application.BaseTracker;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.core.balance.domain.repository.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.bctracker.core.balance.domain.value.BalanceMessage;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.service.ValidatorFilter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 他アカウントまたは他ゾーンの操作により自身の残高が変化したメッセージを受け、
 * 残高キャッシュを更新するトラッカー.
 */
@RequiredArgsConstructor
@Transactional
@Component
@Slf4j
public class BalanceTracker implements BaseTracker<BalanceMessage> {

    private final ValidatorFilter validatorFilter;

    private final BalanceCacheRepository balanceCacheRepository;

    @Override
    public BalanceMessage acceptable(BCEvent<?> event) {
        BalanceMessage message = BalanceMessage.create(event.to());
        if (message == null) {
            return null;
        }

        boolean acceptable = this.validatorFilter.acceptable(message.validatorId());
        if (acceptable == false) {
            return null;
        }

        return message;
    }

    @Override
    public boolean onMessage(BalanceMessage message) {
        // 残高キャッシュを加算
        this.balanceCacheRepository.addBalance(message.accountId(), message.zoneId(), message.amount());
        log.info("Succeed to add balance cache. transactionType : {}, zoneId : {}, accountId : {}, amount : {}",
                 message.transactionType().getValue(), message.zoneId().getValue(), message.accountId().getValue(),
                 message.amount().getValue());

        return true;
    }
}
