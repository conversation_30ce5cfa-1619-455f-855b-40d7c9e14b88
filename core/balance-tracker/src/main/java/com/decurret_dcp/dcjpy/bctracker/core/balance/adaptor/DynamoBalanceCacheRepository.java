package com.decurret_dcp.dcjpy.bctracker.core.balance.adaptor;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.core.balance.config.BalanceTrackerProperty;
import com.decurret_dcp.dcjpy.bctracker.core.balance.domain.repository.BalanceCacheRepository;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.ReturnValue;
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest;

@Slf4j
@AllArgsConstructor
@Repository
public class DynamoBalanceCacheRepository implements BalanceCacheRepository {

    private static final String TIME_ZONE = "Asia/Tokyo";

    private final BalanceTrackerProperty property;

    private final DynamoDbClient dynamoDbClient;

    @Override
    public void addBalance(AccountId accountId, ZoneId zoneId, Amount addAmount) {
        this.calcBalanceCache(accountId, zoneId, addAmount.getValue().longValue());
    }

    private void calcBalanceCache(AccountId accountId, ZoneId zoneId, long amount) {
        UpdateItemRequest request = this.createUpdateBalanceCacheRequest(accountId, zoneId, amount);

        try {
            //更新処理
            this.dynamoDbClient.updateItem(request);
        } catch (DynamoDbException exc) {
            throw new RuntimeException(
                    "Failed to update balance cache. accountId = " + accountId.getValue()
                            + ", zoneId = " + zoneId.getValue()
                            + ", amount = " + amount,
                    exc
            );
        }
    }

    /**
     * 残高キャッシュ更新用のリクエスト生成
     *
     * @param accountId 更新対象アカウントID
     * @param zoneId 更新対象ゾーンID
     * @param amount 加減算額(加算の場合:+,減算の場合:-)
     *
     * @return 生成結果
     */
    private UpdateItemRequest createUpdateBalanceCacheRequest(AccountId accountId, ZoneId zoneId, long amount) {
        String accountIdStr = accountId.getValue();
        String zoneIdStr = zoneId.getValue().toString();

        ZonedDateTime currentDateTime = LocalDateTime.now().atZone(java.time.ZoneId.of(TIME_ZONE));
        String formatDateTime = currentDateTime.format(DateTimeFormatter.ISO_DATE_TIME);

        return UpdateItemRequest.builder()
                .tableName(property.balanceCacheTable)
                .key(Map.ofEntries(
                             Map.entry("account_id", AttributeValue.builder().s(accountIdStr).build()),
                             Map.entry("zone_id", AttributeValue.builder().n(zoneIdStr).build())
                     )
                )
                .conditionExpression("account_id = :expectedAccountId AND zone_id = :expectedZoneId")
                .updateExpression("ADD balance :amount SET updated_at = :currentDateTime")
                .expressionAttributeValues(
                        Map.ofEntries(
                                Map.entry(":expectedAccountId", AttributeValue.builder().s(accountIdStr).build()),
                                Map.entry(":expectedZoneId", AttributeValue.builder().n(zoneIdStr).build()),
                                Map.entry(":amount", AttributeValue.builder().n(String.valueOf(amount)).build()),
                                Map.entry(":currentDateTime", AttributeValue.builder().s(formatDateTime).build())
                        )
                )
                .returnValues(ReturnValue.ALL_NEW)
                .build();
    }
}
