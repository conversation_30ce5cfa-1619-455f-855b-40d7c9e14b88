package com.decurret_dcp.dcjpy.bctracker.core.balance;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import com.decurret_dcp.dcjpy.bctracker.base.config.BCTrackerBaseSqsProperty;
import com.decurret_dcp.dcjpy.bctracker.core.balance.config.BalanceTrackerProperty;
import com.decurret_dcp.dcjpy.bctracker.core.share.config.BCClientProperty;

/**
 * 残高キャッシュ更新 (CORE)
 */
@SpringBootApplication
@ComponentScan("com.decurret_dcp.dcjpy.bctracker")
@EnableConfigurationProperties({ BCTrackerBaseSqsProperty.class, BalanceTrackerProperty.class, BCClientProperty.class })
public class BalanceTrackerMain {

    public static void main(String[] args) {
        SpringApplication.run(BalanceTrackerMain.class, args);
    }
}
