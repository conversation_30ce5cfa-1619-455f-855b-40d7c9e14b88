package com.decurret_dcp.dcjpy.bctracker.core.balance.domain.value;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.TransferEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class BalanceTransferMessage implements BalanceMessage {

    public final AccountId accountId;

    public final ValidatorId toValidatorId;

    public final ZoneId zoneId;

    public final Amount amount;

    public final TransactionType transactionType;

    public AccountId accountId() {
        return this.accountId;
    }

    public ValidatorId fromValidatorId() {return null;}

    public ValidatorId toValidatorId() {
        return this.toValidatorId;
    }

    public ZoneId zoneId() {
        return this.zoneId;
    }

    public Amount amount() {
        return this.amount;
    }

    public TransactionType transactionType() {
        return this.transactionType;
    }

    /**
     * イベントのFilter条件に使用するvalidatorId
     *
     * @return validatorId
     */
    public ValidatorId validatorId() {
        return this.toValidatorId;
    }

    public static BalanceTransferMessage create(TransferEvent event) {
        // DCJPY 移転に関するイベント (FinZone / BizZone 共通)
        if (event.transferType.isTransfer() == true) {
            return BalanceTransferMessage.builder()
                    .accountId(event.toAccountId)
                    .toValidatorId(event.toValidatorId)
                    .zoneId(event.zoneId)
                    .amount(event.amount)
                    .transactionType(TransactionType.TRANSFER)
                    .build();
        }

        // BizZone にてディスチャージを行った結果、IBC 経由で FinZone にて受信するイベント
        if (event.transferType == TransferEventType.DISCHARGE) {
            return BalanceTransferMessage.builder()
                    .accountId(event.toAccountId)
                    .toValidatorId(event.toValidatorId)
                    .zoneId(ZoneId.financialZoneId())
                    .amount(event.amount)
                    .transactionType(TransactionType.DISCHARGE)
                    .build();
        }

        // 上記以外は対象外
        return null;
    }
}
