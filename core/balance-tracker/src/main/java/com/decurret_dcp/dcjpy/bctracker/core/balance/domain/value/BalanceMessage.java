package com.decurret_dcp.dcjpy.bctracker.core.balance.domain.value;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.IssueVoucherEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;

public interface BalanceMessage {

    /** アカウントID. */
    public AccountId accountId();

    public ValidatorId fromValidatorId();

    public ValidatorId toValidatorId();

    public ZoneId zoneId();

    public Amount amount();

    public TransactionType transactionType();

    public ValidatorId validatorId();

    public static BalanceMessage create(BCEventTypeHolder event) {
        return switch (event.eventType()) {
            case TRANSFER -> BalanceTransferMessage.create((TransferEvent) event);
            case ISSUE_VOUCHER -> BalanceIssueVoucherMessage.create((IssueVoucherEvent) event);
            default -> null;
        };
    }
}
