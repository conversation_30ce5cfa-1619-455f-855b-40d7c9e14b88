package com.decurret_dcp.dcjpy.bctracker.core.balance.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.IssueVoucherEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent
import com.decurret_dcp.dcjpy.bctracker.core.balance.domain.value.BalanceMessage
import com.decurret_dcp.dcjpy.bctracker.core.balance.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest()
class BalanceTrackerSpec extends Specification {

    @Autowired
    BalanceTracker balanceTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        objectMapper = new ObjectMapper()
        CoreAdhocHelper.insertBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000", 1000)
        CoreAdhocHelper.insertBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3001", 1000)
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }


    /**
     * TransferイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのindexValuesの値
     */
    private static JsonNode getTransferIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * TransferイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getTransferNonIndexValues() {
        String nonIndexValues = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント２",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
                "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * Transfer(Discharge)イベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return Transfer(Discharge)イベントのindexValuesの値
     */
    private static JsonNode getTransferDischargeIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * Transfer(Discharge)イベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return Transfer(Discharge)イベントのnonIndexValuesの値
     */
    private static JsonNode getTransferDischargeNonIndexValues() {
        String indexValues = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "0",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント１",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                }
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * IssueVoucherイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return IssueイベントのnonIndexValuesの値
     */
    private static JsonNode getIssueVoucherIndexValues() {
        String indexValues = """
            {
                "zoneId": "3001",
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * IssueVoucherイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return IssueVoucherイベントのindexValuesの値
     */
    private static JsonNode getIssueVoucherNonIndexValues() {
        String nonIndexValues = """
            {
                "accountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "accountName": "アカウント1",
                "amount": "2000",
                "balance": "1000",
                "traceId":  [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    def "testAcceptable_処理対象外のイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_Transfer(Fin)イベントでtoValidatorIdがclient_entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()

        String testData = """
            {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_Transfer(Fin)イベントが発生した場合_ #testCase"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()

        String tmp = """
        {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": "3000",
                "fromValidatorId": ${fromValidatorId},
                "toValidatorId": ${toValidatorId},
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": ${sendAccountId},
                "fromAccountId": ${fromAccountId},
                "fromAccountName": "アカウント1",
                "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo",
                "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        }
        """
        JsonNode nonIndexValues = objectMapper.readTree(tmp)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.transactionType() == TransactionType.TRANSFER
        result.toValidatorId().getValue().equals(validatorId1)
        Objects.isNull(result.fromValidatorId())
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        result.zoneId().value == 3000
        result.amount().getValue() == 100

        where:
        testCase                                                                      | fromValidatorId                                                                                           | toValidatorId                                                                                             | fromAccountId                                                                                                                          | sendAccountId                                                                                                                          | validatorId1
        "from_validatorとto_validator, from_accountとsend_accountが異なる場合"        | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
        "from_validatorとto_validatorが同じで、from_accountとsend_accountが異なる場合" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 97] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
        "from_validatorとto_validatorが異なり、form_accountとsend_accountが同じ場合"   | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
        "from_validatorとto_validator、from_accountとsend_accountが同じ場合"           | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]" | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | [54, 48, 98, 79, 118, 66, 56, 76, 51, 86, 120, 89, 83, 67, 77, 53, 81, 87, 100, 49, 87, 112, 83, 78, 101, 110, 71, 97, 71, 70, 98, 98] | "80bOvB8L3VxYSCM5QWd1WpSNenGaGFba"
    }

    def "testAcceptable_Transfer(Discharge)イベントが発生した場合_ #testCase"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        JsonNode nonIndexValues = getTransferDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.transactionType() == TransactionType.DISCHARGE
        result.toValidatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        Objects.isNull(result.fromValidatorId())
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        result.zoneId().value == 3000
        result.amount().getValue() == 100
    }

    def "testAcceptable_Transfer(Discharge)イベントが発生した場合_ miscValue2が複数"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "0",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント１",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5,310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p6",
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.transactionType() == TransactionType.DISCHARGE
        result.toValidatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        Objects.isNull(result.fromValidatorId())
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        result.zoneId().value == 3000
        result.amount().getValue() == 100
    }

    def "testAcceptable_Transfer(Discharge)イベントが発生した場合_ miscValue2が4096文字"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [100,105,115,99,104,97,114,103,101,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "0",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "30000",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント１",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "${'a' * 4096}",
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.transactionType() == TransactionType.DISCHARGE
        result.toValidatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        Objects.isNull(result.fromValidatorId())
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        result.zoneId().value == 3000
        result.amount().getValue() == 100
    }

    def "testAcceptable_Transferイベント(処理対象外)発生した場合_#testCase"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        String testData = """
            {
                "transferData": {
                    "transferType": [99,104,97,114,103,101 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "0",
                    "businessZoneBalance": "0",
                    "bizZoneId": "3001",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": $fromAccountId,
                    "fromAccountName": "アカウント1",
                    "toAccountId": $toAccountId,
                    "toAccountName": "アカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                }
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)

        where:
        testCase                                                                  | fromAccountId                                                                                             | toAccountId
        "fromAccountId、toAccountId両方がEscrowAccountアカウント"                  | "[54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "[54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]"
        "fromAccountIdがEscrowアカウントでないかつ、toAccountIdがEscrowアカウント" | "[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "[54,49,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]"

    }

    def "testAcceptable_IssueVoucherイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getIssueVoucherIndexValues()
        JsonNode nonIndexValues = getIssueVoucherNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("IssueVoucher")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        result.transactionType() == TransactionType.CHARGE
        result.fromValidatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        Objects.isNull(result.toValidatorId())
        result.zoneId().getValue() == 3001
        result.amount().getValue() == 2000
    }

    def "testAcceptable_IssueVoucherイベントで、validatorIdがclient entityテーブルに存在しない場合"() {
        setup:

        String testData = """
            {
                "zoneId": "3001",
                "validatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode indexValues = objectMapper.readTree(testData)
        JsonNode nonIndexValues = getIssueVoucherNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("IssueVoucher")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.balanceTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testOnMessage_Transfer(Fin)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        BalanceMessage balanceMessage = BalanceMessage.create(transferEvent)

        when:
        def result = this.balanceTracker.onMessage(balanceMessage)

        then:
        result == true
        def balanceCache = CoreAdhocHelper.getBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        balanceCache.get("zone_id").n().equals("3000")
        balanceCache.get("account_id").s().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        balanceCache.get("balance").n().equals("1100")
        !Objects.isNull(balanceCache.get("updated_at").s())

        cleanup:
        CoreAdhocHelper.deleteBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        CoreAdhocHelper.insertBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000", 1000)
    }

    def "testOnMessage_Transfer(Fin)イベントが発生した場合_miscValue2が複数"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント２",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5,310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p6",
                    "memo": "メモmemo"
                },
                "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        BalanceMessage balanceMessage = BalanceMessage.create(transferEvent)

        when:
        def result = this.balanceTracker.onMessage(balanceMessage)

        then:
        result == true
        def balanceCache = CoreAdhocHelper.getBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        balanceCache.get("zone_id").n().equals("3000")
        balanceCache.get("account_id").s().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        balanceCache.get("balance").n().equals("1100")
        !Objects.isNull(balanceCache.get("updated_at").s())

        cleanup:
        CoreAdhocHelper.deleteBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        CoreAdhocHelper.insertBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000", 1000)
    }

    def "testOnMessage_Transfer(Fin)イベントが発生した場合_miscValue2が4096文字"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "アカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "アカウント２",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "${'a' * 4096}",
                    "memo": "メモmemo"
                },
                "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        BalanceMessage balanceMessage = BalanceMessage.create(transferEvent)

        when:
        def result = this.balanceTracker.onMessage(balanceMessage)

        then:
        result == true
        def balanceCache = CoreAdhocHelper.getBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        balanceCache.get("zone_id").n().equals("3000")
        balanceCache.get("account_id").s().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        balanceCache.get("balance").n().equals("1100")
        !Objects.isNull(balanceCache.get("updated_at").s())

        cleanup:
        CoreAdhocHelper.deleteBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        CoreAdhocHelper.insertBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000", 1000)
    }

    def "testOnMessage_Transfer(Fin)イベントが発生した場合_ #testCase"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        String testData = """
            {
            "transferData": {
                "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "zoneId": ${zoneId},
                "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountBalance": "10000",
                "toAccountBalance": "20000",
                "businessZoneBalance": "0",
                "bizZoneId": "0",
                "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "fromAccountName": "アカウント1",
                "toAccountId": ${accountId},
                "toAccountName": "アカウント2",
                "amount": "100",
                "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                "memo": "メモmemo"},
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        BalanceMessage balanceMessage = BalanceMessage.create(transferEvent)

        when:
        this.balanceTracker.onMessage(balanceMessage)

        then:
        // dynamoDBの更新でRuntimeExceptionが発生する
        thrown(RuntimeException)

        def balanceCache = CoreAdhocHelper.getBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        // 初期値から変更されていないこと
        balanceCache.get("zone_id").n().equals("3000")
        balanceCache.get("account_id").s().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        balanceCache.get("balance").n().equals("1000")
        !Objects.isNull(balanceCache.get("updated_at").s())

        where:
        testCase                                                | accountId                                                                                                 | zoneId
        "指定されたAccountIdがDynamoDBに存在しない場合"         | "[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,0,0]"   | "3000"
        "指定されたzoneIdがDynamoDBに存在しない場合"            | "[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99]" | "9999"
        "指定されたAccountIDとZoneIdがDynamoDBに存在しない場合" | "[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,0,0]"   | "9999"
    }

    def "testOnMessage_Transfer(Discharge)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferDischargeIndexValues()
        JsonNode nonIndexValues = getTransferDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        BalanceMessage balanceMessage = BalanceMessage.create(transferEvent)

        when:
        def result = this.balanceTracker.onMessage(balanceMessage)

        then:
        result == true
        def balanceCache = CoreAdhocHelper.getBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        balanceCache.get("zone_id").n().equals("3000")
        balanceCache.get("account_id").s().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        balanceCache.get("balance").n().equals("1100")
        !Objects.isNull(balanceCache.get("updated_at").s())

        cleanup:
        CoreAdhocHelper.deleteBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000")
        CoreAdhocHelper.insertBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3000", 1000)
    }

    def "testOnMessage_IssueVoucherイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getIssueVoucherIndexValues()
        JsonNode nonIndexValues = getIssueVoucherNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("IssueVoucher")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        IssueVoucherEvent issueVoucherEvent = IssueVoucherEvent.create(testEvent)
        BalanceMessage balanceMessage = BalanceMessage.create(issueVoucherEvent)

        when:
        def result = this.balanceTracker.onMessage(balanceMessage)

        then:
        result == true
        def balanceCache = CoreAdhocHelper.getBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3001")
        // 初期値から変更されていないこと
        balanceCache.get("zone_id").n().equals("3001")
        balanceCache.get("account_id").s().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        balanceCache.get("balance").n().equals("3000")
        !Objects.isNull(balanceCache.get("updated_at").s())

        cleanup:
        CoreAdhocHelper.deleteBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3001")
        CoreAdhocHelper.insertBalanceCache("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc", "3001", 1000)
    }

}
