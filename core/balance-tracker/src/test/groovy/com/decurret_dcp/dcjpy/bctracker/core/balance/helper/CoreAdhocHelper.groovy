package com.decurret_dcp.dcjpy.bctracker.core.balance.helper

import groovy.sql.Sql
import org.springframework.test.context.DynamicPropertyRegistry
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest
import software.amazon.awssdk.services.sqs.model.QueueAttributeName

class CoreAdhocHelper {

    private static final int DB_POOL_CONNECTION_IDLE_SIZE = 2

    private static final ObjectMapper mapper = new ObjectMapper()

    private static dbPort

    private static localStackPort

    private static DockerComposeContainer composeContainer

    private static connectionCount = 0

    static Sql sql

    static SqsClient sqsClient

    static String queueUri

    static String getDbPort() { return dbPort }

    static String getLocalStackPort() { return localStackPort }

    static {
        startContainer()
        createSQS("dcjpy_bctracker_queue_balance.fifo")
        createDynamoDBTable()
    }

    private static void startContainer() {
        // dbが起動した後にpostgresにアクセスできるように、Wait.forLogMessage()でアクセス可能になるログが出力されるまで待つ
        composeContainer = new DockerComposeContainer(new File("../docker-compose_cicd.yml"))
                .withExposedService("db", 5432)
                .withExposedService("localstack", 4566)
                .waitingFor("db", Wait.forListeningPort())
                .waitingFor("localstack", Wait.forListeningPort())
                .waitingFor("db", Wait.forLogMessage(".*database system is ready to accept connections.*\\n", 1))

        composeContainer.start()
        dbPort = String.valueOf(composeContainer.getServicePort("db", 5432))
        localStackPort = String.valueOf(composeContainer.getServicePort("localstack", 4566))
    }

    static Sql initAdhoc(DynamicPropertyRegistry registry) {
        var jdbcUrl = "***************************:${dbPort}/postgres"

        registry.add("spring.datasource.url", () -> jdbcUrl)
        registry.add("bctracker.base.sqs.local-endpoint", () -> "http://localhost:${localStackPort}")

        // テストではコネクションを多く用意する必要はないので、少なめにする
        registry.add("spring.datasource.hikari.minimum-idle", () -> DB_POOL_CONNECTION_IDLE_SIZE)

        sql = Sql.newInstance(jdbcUrl, "postgres", "postgres", "org.postgresql.Driver")

        sql.execute("DROP SCHEMA if EXISTS PUBLIC CASCADE")
        sql.execute("CREATE SCHEMA public")
        // DDL およびマスタデータの投入
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000001__create_table.sql").text)

        // テスト用データの投入
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000011__test_init_client_entity.sql").text)
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000012__test_init_entity_signer.sql").text)
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000013__test_init_messages.sql").text)

        return sql
    }

    static void cleanupSpec() {
        sql.close()
        sqsClient.close()

        // テストクラスごとにSpringBootを起動しているにも関わらず、
        // なぜか以前のテストケースでのDBコネクションが残ったままなので、
        // デフォルトのDBコネクションプールのサイズを超えたら、コンテナを再起動する
        connectionCount += DB_POOL_CONNECTION_IDLE_SIZE
        if (connectionCount >= 100) {
            composeContainer.stop()
            connectionCount = 0

            startContainer()
        }
    }

    private static void createSQS(String queueName) {
        sqsClient = SqsClient.builder()
                .region(Region.AP_NORTHEAST_1)
                .endpointOverride(URI.create("http://localhost:${localStackPort}"))
                .build()

        Map<QueueAttributeName, String> createQueueParams = new HashMap<>()
        createQueueParams.put(QueueAttributeName.FIFO_QUEUE, "true")
        createQueueParams.put(QueueAttributeName.VISIBILITY_TIMEOUT, "30")
        createQueueParams.put(QueueAttributeName.CONTENT_BASED_DEDUPLICATION, "true")

        def createQueueRequest = CreateQueueRequest.builder()
                .queueName(queueName)
                .attributes(createQueueParams)
                .build()

        def response = sqsClient.createQueue(createQueueRequest)
        queueUri = response.queueUrl()

        sqsClient.close()
    }

    private static void createDynamoDBTable() {

        def dynamoDbClient = DynamoDbClient.builder()
                .region(Region.AP_NORTHEAST_1) // 適切なリージョンを指定
                .endpointOverride(URI.create("http://localhost:${localStackPort}")) // DynamoDB Localのエンドポイントを指定
                .build()

        try {
            CreateTableRequest createTableRequest = CreateTableRequest.builder()
                    .tableName("balance_cache") // テーブル名を指定
                    .keySchema(
                            KeySchemaElement.builder().attributeName("account_id").keyType(KeyType.HASH).build(),
                            KeySchemaElement.builder().attributeName("zone_id").keyType(KeyType.RANGE).build()
                    )
                    .attributeDefinitions(
                            AttributeDefinition.builder().attributeName("account_id").attributeType(ScalarAttributeType.S).build(),
                            AttributeDefinition.builder().attributeName("zone_id").attributeType(ScalarAttributeType.N).build()
                    )
                    .provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
                    .build() as CreateTableRequest

            dynamoDbClient.createTable(createTableRequest)

            println("Table created successfully.")
        } finally {
            dynamoDbClient.close()
        }
    }

    static void insertBalanceCache(String accountId, String zoneId, int balance) {

        def dynamoDbClient = DynamoDbClient.builder()
                .region(Region.AP_NORTHEAST_1) // 適切なリージョンを指定
                .endpointOverride(URI.create("http://localhost:" + localStackPort)) // DynamoDB Localのエンドポイントを指定
                .build()

        try {
            Map<String, AttributeValue> item = [
                    'account_id': AttributeValue.builder().s(accountId).build(),
                    'zone_id'   : AttributeValue.builder().n(zoneId).build(),
                    'balance'   : AttributeValue.builder().n(balance.toString()).build(),
                    'updated_at': AttributeValue.builder().s('2024-01-01T00:00:00+09:00').build()
            ]

            PutItemRequest putItemRequest = PutItemRequest.builder()
                    .tableName('balance_cache') // テーブル名を指定
                    .item(item)
                    .build()

            dynamoDbClient.putItem(putItemRequest)

            println("Item added successfully.")
        } finally {
            dynamoDbClient.close()
        }
    }

    static void deleteBalanceCache(String accountId, String zoneId) {
        def dynamoDbClient = DynamoDbClient.builder()
                .region(Region.AP_NORTHEAST_1) // 適切なリージョンを指定
                .endpointOverride(URI.create("http://localhost:" + localStackPort)) // DynamoDB Localのエンドポイントを指定
                .build()

        try {
            Map<String, AttributeValue> key = [
                    'account_id': AttributeValue.builder().s(accountId).build(),
                    'zone_id'   : AttributeValue.builder().n(zoneId).build()
            ]

            DeleteItemRequest deleteItemRequest = DeleteItemRequest.builder()
                    .tableName('balance_cache')
                    .key(key)
                    .build()

            dynamoDbClient.deleteItem(deleteItemRequest)

            println("Item deleted successfully.")
        } finally {
            dynamoDbClient.close()
        }
    }

    static Map<String, AttributeValue> getBalanceCache(String accountId, String zoneId) {
        def dynamoDbClient = DynamoDbClient.builder()
                .region(Region.AP_NORTHEAST_1) // 適切なリージョンを指定
                .endpointOverride(URI.create("http://localhost:" + localStackPort)) // DynamoDB Localのエンドポイントを指定
                .build()

        try {
            Map<String, AttributeValue> key = [
                    'account_id': AttributeValue.builder().s(accountId).build(),
                    'zone_id'   : AttributeValue.builder().n(zoneId).build()
            ]

            GetItemRequest getItemRequest = GetItemRequest.builder()
                    .tableName('balance_cache') // テーブル名を指定
                    .key(key)
                    .build()

            return dynamoDbClient.getItem(getItemRequest).item()
        } finally {
            dynamoDbClient.close()
        }
    }

}
