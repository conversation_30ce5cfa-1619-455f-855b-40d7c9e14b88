-- client_entity
create table client_entity
(
    client_id   varchar(64) NOT NULL UNIQUE,
    entity_type varchar(16) NOT NULL,
    zone_id     numeric     NOT NULL,
    entity_id   varchar(64) NOT NULL UNIQUE,
    primary key (client_id)
);

-- entity_signer
create table entity_signer
(
    entity_id varchar(64)   NOT NULL UNIQUE,
    zone_id   numeric(4)    NOT NULL,
    key_id    varchar(1000) NOT NULL,
    key_type  varchar(16)   NOT NULL,
    eoa       varchar(42)   NOT NULL,
    primary key (entity_id)
);

-- nft_metadata
create table nft_metadata
(
    nft_id          varchar(64)              NOT NULL,
    metadata_id     varchar(64)              NOT NULL,
    zone_id         numeric(4)               NOT NULL,
    metadata_detail json                     NOT NULL,
    minted_at       timestamp with time zone NOT NULL,
    primary key (nft_id, metadata_id, zone_id)
);

-- transaction
create table transaction
(
    transaction_id     varchar(36) NOT NULL UNIQUE,
    transaction_hash   varchar(66) NOT NULL,
    account_id         varchar(64) NOT NULL,
    account_name       varchar(48),
    validator_id       varchar(64) NOT NULL,
    zone_id            numeric(4)  NOT NULL,
    transacted_at      timestamp   NOT NULL,
    transaction_type   varchar(32) NOT NULL,
    amount             numeric     NOT NULL,
    post_balance       numeric     NOT NULL,
    other_account_id   varchar(64),
    other_account_name varchar(48),
    other_zone_id      numeric(4),
    primary key (transaction_id)
);

create index transaction_idx1 on transaction (account_id, transacted_at);
create index transaction_idx2 on transaction (transaction_hash);

-- transaction_sender
create table transaction_sender
(
    transaction_id    varchar(36) NOT NULL UNIQUE,
    send_account_id   varchar(64) NOT NULL,
    primary key (transaction_id)
);

-- transaction_misc
create table transaction_misc
(
    transaction_id   varchar(36) NOT NULL UNIQUE,
    misc_value1      varchar(32),
    misc_value2      TEXT,
    primary key (transaction_id)
);

-- transaction_memo
create table transaction_memo
(
    transaction_id   varchar(36) NOT NULL UNIQUE,
    memo             varchar(48),
    primary key (transaction_id)
);

-- messages
create table messages
(
    message_code varchar(5) NOT NULL UNIQUE,
    message_body varchar(100) NOT NULL ,
    primary key (message_code)
);
