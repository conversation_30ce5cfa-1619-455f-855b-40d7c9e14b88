TRUNCATE messages;

INSERT INTO messages (message_code, message_body) VALUES
    ('E0001','E0001:account_id not found'),
    ('E0002','E0002:send_account_id not found'),
    ('E0003','E0003:from_account_id not found'),
    ('E0004','E0004:to_account_id not found'),
    ('E0005','E0005:balance not enough'),
    ('E0006','E0006:command is null'),
    ('E0007','E0007:contractName is null'),
    ('E0008','E0008:deposit not found'),
    ('E0009','E0009:deposit overflow'),
    ('E0010','E0010:deposit underflow'),
    ('E0011','E0011:must be 3000 or 3001-3fff'),
    ('E0012','E0012:no items to update'),
    ('E0013','E0013:mint_amount exceeds daily limit'),
    ('E0014','E0014:Insufficient authority'),
    ('E0015','E0015:Internal Server Error'),
    ('E0016','E0016:issuer_id not enabled'),
    ('E0017','E0017:issuer_id not found'),
    ('E0018','E0018:method is empty string'),
    ('E0019','E0019:method is null'),
    ('E0020','E0020:owner_account_id not found'),
    ('E0021','E0021:spender_account_id not found'),
    ('E0022','E0022:burn_amount exceeds daily limit'),
    ('E0023',''),
    ('E0024','E0024:transfer_amount exceeds daily limit'),
    ('E0025','E0025:exchange_amount exceeds daily limit'),
    ('E0026',''),
    ('E0027','E0027:provider_id not enabled'),
    ('E0028','E0028:provider_id not found'),
    ('E0029',''),
    ('E0030',''),
    ('E0031',''),
    ('E0032','E0032:account_id is terminated'),
    ('E0033','E0033:send_account_id is terminated'),
    ('E0034','E0034:to_account_id is terminated'),
    ('E0035','E0035:spender_account_id is terminated'),
    ('E0036','E0036:token_id not enabled'),
    ('E0037','E0037:token_id not found'),
    ('E0038','E0038:provider not exist'),
    ('E0039','E0039:Unauthorized'),
    ('E0040','E0040:Unknown signer'),
    ('E0041',''),
    ('E0042','E0042:validator_id not found'),
    ('E0043','E0043:issuer_id:must not be empty'),
    ('E0044','E0044:issuer_id:must be empty'),
    ('E0045','E0045:account_id not enabled'),
    ('E0046','E0046:token not exist'),
    ('E0047',''),
    ('E0048','E0048:from_date:The date format is incorrect'),
    ('E0049','E0049:to_date:The date format is incorrect'),
    ('E0050','E0050:transaction_hash not found'),
    ('E0051',''),
    ('E0052',''),
    ('E0053','E0053:from_date must be before to_date'),
    ('E0054',''),
    ('E0055','E0055:account_id already exists'),
    ('E0056','E0056:balance exists'),
    ('E0057','E0057:The URL contains characters that cannot be used'),
    ('E0058',''),
    ('E0059','E0059:account_id not identified'),
    ('E0060','E0060:json error'),
    ('E0061',''),
    ('E0062','E0062:account_signature is invalid'),
    ('E0063','E0063:info is invalid'),
    ('E0064','E0064:owner_account_id and spender_account_id cannot be the same'),
    ('E0065','E0065:to_region invalid'),
    ('E0066','E0066:from_account_id and to_account_id cannot be the same'),
    ('E0067','E0067:hexadecimal string cannot be set for misc_value'),
    ('E0068','E0068:account identity not found'),
    ('E0069',''),
    ('E0070','E0070:mint_amount exceeds mint limit'),
    ('E0071','E0071:burn_amount exceeds burn limit'),
    ('E0072','E0072:transfer_amount exceeds transfer limit'),
    ('E0073','E0073:exchange_amount exceeds exchange limit'),
    ('E0074','E0074:the biz zone account is not in a state where it can be terminated'),
    ('E0075','E0075:this request_id has been processed'),
    ('E0076','E0076:Invalid format. '),
    ('E0077','E0077:business zone account not found'),
    ('E0078','E0078:business zone account status is invalid'),
    ('E0083','E0083:BizZone balance exists'),
    ('E0084','E0084:account_id status is not frozen')
    ;