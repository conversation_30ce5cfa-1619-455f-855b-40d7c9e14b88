#!/usr/bin/env bash

# set -euo pipefail

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

enqueue() {
    local BODY=$1
    DEDUPLICATION_ID=$(shuf -i 1-100 -n 1)
    aws --endpoint-url=http://localhost:14566 sqs send-message \
        --queue-url http://localhost:14566/000000000000/dcjpy_bctracker_queue_transaction.fifo \
        --message-body "${BODY}" \
        --message-deduplication-id "${DEDUPLICATION_ID}" \
        --message-group-id "group-id-2"
}
enqueue '{
            "Type": "Notification",
          	"MessageId": "b1f3c9d8-8096-5d85-b1a8-975e2ce4fd9f",
          	"SequenceNumber": "10000000000000019000",
          	"TopicArn": "arn:aws:sns:ap-northeast-1:************:prod-biz-a-tokyo-bcmonitoring-stream.fifo",
            "Message": "{\"eventID\": \"9ea2f5fe8942b88f52d57d70e63e2a50\",\"eventName\": \"INSERT\",\"eventVersion\": \"1.1\",\"eventSource\": \"aws:dynamodb\",\"awsRegion\": \"ap-northeast-1\",\"dynamodb\": {\"ApproximateCreationDateTime\": 1594874888,\"Keys\": {\"logIndex\": {\"N\": \"0\"},\"transactionHash\": {\"S\": \"0x8a89862ceb2069f1aea8d7104133d31563f4d750851608b1b0eb5bc78d3b2add\"}},\"NewImage\": {\"logIndex\": {\"N\": \"0\"},\"log\": {\"S\": \"{\\\"address\\\":\\\"0xa98eb6579c00dc5a29ee1d3567dd681375afe97d\\\",\\\"topics\\\":[\\\"0x92a5b55b9de3b08864c6f594a441cb49e37d10315fca73e5d8ec5bc3c68dda04\\\",\\\"0x0000000000000000000000000000000000000000000000000000000000003001\\\"],\\\"data\\\":\\\"0x000000000000000000000000000060001ff5734f630f4309903ca4c70c1826cd0000000000000000000000000000000000000000000000000000000000002710\\\",\\\"blockNumber\\\":\\\"0x2b6eb4\\\",\\\"transactionHash\\\":\\\"0x8a89862ceb2069f1aea8d7104133d31563f4d750851608b1b0eb5bc78d3b2add\\\",\\\"transactionIndex\\\":\\\"0x0\\\",\\\"blockHash\\\":\\\"0xf0b527f4e25d79deae31c96555c3b54399d6dcc2e32823bbf38e57673cfdf24e\\\",\\\"logIndex\\\":\\\"0x0\\\",\\\"removed\\\":false}\"},\"blockTimestamp\": {\"N\": \"**********\"},\"name\": {\"S\": \"RedeemVoucher\"},\"transactionHash\": {\"S\": \"0x8a89862ceb2069f1aea8d7104133d31563f4d750851608b1b0eb5bc78d3b2add\"},\"indexedValues\": {\"S\": \"{\\\"zoneId\\\":3001,\\\"validatorId\\\":[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]}\"},\"nonIndexedValues\": {\"S\": \"{\\\"accountId\\\":[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],\\\"accountName\\\":[65,99,99,111,117,110,116,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\\\"amount\\\":10000,\\\"balance\\\":10000}\"}},\"SequenceNumber\": \"46474900000000004188509015\",\"SizeBytes\": 2319,\"StreamViewType\": \"NEW_AND_OLD_IMAGES\"},\"eventSourceARN\": \"arn:aws:dynamodb:ap-northeast-1:************:table/tsys-vetification-Events/stream/2020-07-07T06:11:40.425\"}",
            "Timestamp": "2024-04-03T01:00:34.433Z",
            "UnsubscribeURL": "https://sns.ap-northeast-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:ap-northeast-1:************:prod-biz-a-tokyo-bcmonitoring-stream.fifo:8d1a841d-ca35-4ae2-869b-4d4e9eb1a48c"
}'
