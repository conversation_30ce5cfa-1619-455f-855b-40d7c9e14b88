'use strict';

const express = require('express');

// Constants
const PORT = 8080;
const HOST = '0.0.0.0';
const app = express();

// requires modules
const fs = require("fs");
const jwt = require('jsonwebtoken');
const uuid = require('uuidjs');
const njwk = require('node-jwk');

app.get('/token/:clientId', (req, res) => {
    let privateKey = fs.readFileSync("private/privateKey.json", 'utf8');
    const myKey = njwk.JWK.fromJSON(privateKey)

    var claims = {
        sub: req.params.clientId,
        token_use: "access",
        scope: "dummy/test",
        auth_time: Math.floor(Date.now() / 1000),
        iat: Math.floor(Date.now() / 1000),
        version: 2,
        jti: uuid.generate(),
        client_id: req.params.clientId
    }

    var token = jwt.sign(claims, myKey.key.toPrivateKeyPEM(), {
        issuer: 'http://localhost:8082',
        expiresIn: 60 * 60 * 24 * 365,
        algorithm: 'RS256',
        keyid: myKey.kid
    });
    console.log(token)
    res.send(token);
});

app.post('/oauth2/token', (req, res) => {
    console.log("/oauth2/token");
    let privateKey = fs.readFileSync("private/privateKey.json", 'utf8');
    const myKey = njwk.JWK.fromJSON(privateKey)

    let b64auth = (req.headers.authorization || '').split(' ')[1] || '';
    let [user, pass] = Buffer.from(b64auth, 'base64').toString().split(':');

    var claims = {
        sub: user,
        token_use: "access",
        scope: "dummy/test",
        auth_time: Math.floor(Date.now() / 1000),
        iat: Math.floor(Date.now() / 1000),
        version: 2,
        jti: uuid.generate(),
        client_id: user
    }

    var token = jwt.sign(claims, myKey.key.toPrivateKeyPEM(), {
        issuer: 'http://localhost:8082',
        expiresIn: 60 * 60 * 24 * 365,
        algorithm: 'RS256',
        keyid: myKey.kid
    });
    console.log(token)
    res.json({access_token: token, username: user});
});

app.use(express.static('public'));
app.listen(PORT, HOST);
console.log(`Start server on http://${HOST}:${PORT}`);
