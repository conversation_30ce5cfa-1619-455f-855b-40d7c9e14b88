package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.value;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Value;

@Value
@JsonDeserialize(using = ResponseContractBytes32ValueJson.Deserializer.class)
public class ResponseContractBytes32Value {

    private final String value;

    public ResponseContractBytes32Value(String value) {
        this.value = value;
    }
}
