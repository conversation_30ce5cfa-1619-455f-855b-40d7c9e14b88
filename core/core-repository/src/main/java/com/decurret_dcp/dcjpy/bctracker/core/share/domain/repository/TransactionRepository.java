package com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionId;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntities;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionMemoEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionMiscEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionSenderEntity;

public interface TransactionRepository {

    public void registerTransaction(TransactionEntity transaction);

    public void registerTransactionMemo(TransactionMemoEntity transactionMemoEntity);

    public void registerTransactionMisc(TransactionMiscEntity transactionMiscEntity);

    public void registerTransactionSender(TransactionSenderEntity transactionSenderEntity);

    public boolean existsForTransactionId(TransactionId transactionId);

    public void resisterTransferTransaction(TransactionEntities transactionEntities);
}
