package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.SleepUtil;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionId;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao.TransactionDao;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao.TransactionMemoDao;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao.TransactionMiscDao;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao.TransactionSenderDao;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntities;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionMemoEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionMiscEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionSenderEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.TransactionRepository;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaTransactionRepository implements TransactionRepository {

    private final TransactionDao transactionDao;
    private final TransactionMemoDao transactionMemoDao;
    private final TransactionMiscDao transactionMiscDao;
    private final TransactionSenderDao transactionSenderDao;

    @Override
    public void registerTransaction(TransactionEntity transaction) {
        TransactionId transactionId = initTransactionId();
        TransactionEntity entity = transaction.toBuilder().transactionId(transactionId).build();
        transactionDao.insert(entity);
    }

    @Override
    public void registerTransactionMemo(TransactionMemoEntity transactionMemoEntity) {
        transactionMemoDao.insert(transactionMemoEntity);
    }

    @Override
    public void registerTransactionMisc(TransactionMiscEntity transactionMiscEntity) {
        transactionMiscDao.insert(transactionMiscEntity);
    }

    @Override
    public void registerTransactionSender(TransactionSenderEntity transactionSenderEntity) {
        transactionSenderDao.insert(transactionSenderEntity);
    }

    @Override
    public boolean existsForTransactionId(TransactionId transactionId) {
        return transactionDao.existsForTransactionId(transactionId);
    }

    @Override
    public void resisterTransferTransaction(
            TransactionEntities transactionEntities) {

        TransactionId transactionId = initTransactionId();
        TransactionEntity transactionEntity = transactionEntities.transaction;
        TransactionSenderEntity transactionSenderEntity = transactionEntities.transactionSender;
        TransactionMiscEntity transactionMiscEntity = transactionEntities.transactionMisc;
        TransactionMemoEntity transactionMemoEntity = transactionEntities.transactionMemo;

        // 取引履歴
        transactionDao.insert(transactionEntity.toBuilder().transactionId(transactionId).build());

        // 取引履歴 送金指示者登録
        if (transactionSenderEntity != null) {
            transactionSenderDao.insert(transactionSenderEntity.toBuilder().transactionId(transactionId).build());
        }

        // 取引履歴 misc値
        if (transactionMiscEntity != null) {
            transactionMiscDao.insert(transactionMiscEntity.toBuilder().transactionId(transactionId).build());
        }

        // 取引履歴 memo
        if (transactionMemoEntity != null) {
            transactionMemoDao.insert(transactionMemoEntity.toBuilder().transactionId(transactionId).build());
        }
    }

    /**
     * トランザクションIDを生成する.
     *
     * @return トランザクションID.
     */
    private TransactionId initTransactionId() {
        for (int index = 0; index < 3; index++) {
            TransactionId transactionId = TransactionId.generate();
            boolean alreadyExists = this.transactionDao.existsForTransactionId(transactionId);
            if (!alreadyExists) {
                return transactionId;
            }

            SleepUtil.sleepSilently(100L);
        }

        throw new IllegalStateException("Failed to generate transactionId.");
    }
}
