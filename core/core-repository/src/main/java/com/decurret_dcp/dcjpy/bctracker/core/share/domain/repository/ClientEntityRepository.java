package com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.ClientEntity;

public interface ClientEntityRepository {

    public boolean isExists(ValidatorId validatorId);

    public ZoneId findZoneIdByEntityId(EntityId entityId);

    public ClientEntity findClientEntityId(EntityId entityId);
}
