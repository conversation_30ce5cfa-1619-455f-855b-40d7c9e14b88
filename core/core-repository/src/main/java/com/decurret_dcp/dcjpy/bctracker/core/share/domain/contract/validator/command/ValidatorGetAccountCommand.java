package com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.command;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class ValidatorGetAccountCommand {

    public final ZoneId zoneId;

    public final ValidatorId validatorId;

    public final AccountId accountId;
}
