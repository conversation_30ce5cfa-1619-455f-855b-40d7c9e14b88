package com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.result;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class ValidatorGetAccountResult {

    public final String accountName;

    public final AccountStatus accountStatus;

    public final String reasonCode;

    public final Balance balance;

    public final AppTimeStamp appliedAt;

    public final AppTimeStamp registeredAt;

    public final AppTimeStamp terminatingAt;

    public final AppTimeStamp terminatedAt;
}
