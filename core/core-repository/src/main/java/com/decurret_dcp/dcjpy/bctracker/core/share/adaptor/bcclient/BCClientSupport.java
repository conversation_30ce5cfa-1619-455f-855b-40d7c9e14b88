package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient;

import java.time.Duration;
import java.util.function.Supplier;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.decurret_dcp.dcjpy.bctracker.base.adaptor.RestTemplateLoggingInterceptor;
import com.decurret_dcp.dcjpy.bctracker.base.domain.service.TraceIdHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TraceId;
import com.decurret_dcp.dcjpy.bctracker.core.share.config.BCClientProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

@Component
public class BCClientSupport {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private final RestTemplate restTemplate;

    public BCClientSupport(ObjectMapper objectMapper, RestTemplateBuilder builder, BCClientProperty property) {
        this.restTemplate = initRestTemplate(objectMapper, builder, property);
    }

    static RestTemplate initRestTemplate(
            ObjectMapper objectMapper, RestTemplateBuilder builder, BCClientProperty property
    ) {
        // BCクライアントのAPI仕様に合わせてObjectMapperの設定をカスタマイズ
        // デフォルトのObjectMapperへの影響を避けるため、設定をコピーした新しいインスタンスを生成している
        ObjectMapper mapper = objectMapper.copy();
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE);

        return builder.messageConverters(new MappingJackson2HttpMessageConverter(mapper))
                .rootUri(property.baseUrl)
                .setReadTimeout(Duration.ofMillis(property.readTimeoutMillisecond))
                .requestFactory(initFactorySupplier(property))
                .additionalInterceptors(initTraceIdInterceptor(), new RestTemplateLoggingInterceptor())
                .build();
    }

    private static Supplier<ClientHttpRequestFactory> initFactorySupplier(BCClientProperty property) {
        PoolingHttpClientConnectionManager poolManager = new PoolingHttpClientConnectionManager();
        poolManager.setMaxTotal(property.httpConnectionMaxTotal.intValue());
        poolManager.setDefaultMaxPerRoute(property.httpConnectionMaxPerRoute.intValue());

        CloseableHttpClient client = HttpClientBuilder.create()
                .setConnectionManager(poolManager)
                .build();
        ClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(client);

        return () -> factory;
    }

    private static ClientHttpRequestInterceptor initTraceIdInterceptor() {
        return (request, body, execution) -> {
            TraceId traceId = TraceIdHolder.getTraceId();
            if (traceId != null) {
                request.getHeaders().set("trace-id", traceId.getValue());
            }

            return execution.execute(request, body);
        };
    }

    public <RESPONSE> Either<RESPONSE> call(BCClientCallRequest request, Class<RESPONSE> responseClass) {
        BCClientCallResponse response = this.restTemplate.postForObject("/call", request, BCClientCallResponse.class);
        if (response == null) {
            return Either.failure(
                    "", "Failed to call blockchain contract.",
                    new RuntimeException("Failed to call blockchain contract."));
        }

        if (response.isSuccess() == false) {
            String errorMessage = response.getError();
            String errorCode = initErrorCode(errorMessage);

            return Either.failure(errorCode, errorMessage);
        }

        RESPONSE success = OBJECT_MAPPER.convertValue(response.data, responseClass);

        return Either.success(success);
    }

    private static String initErrorCode(String errorMessage) {
        int endIdx = errorMessage.indexOf(":");
        return (endIdx >= 0) ? errorMessage.substring(0, endIdx) : errorMessage;
    }
}
