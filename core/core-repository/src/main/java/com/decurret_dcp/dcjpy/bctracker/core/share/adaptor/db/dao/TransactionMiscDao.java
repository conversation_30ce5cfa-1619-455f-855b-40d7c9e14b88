package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Insert;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionMiscEntity;

@ConfigAutowireable
@Dao
public interface TransactionMiscDao {

    @Insert
    public Result<TransactionMiscEntity> insert(TransactionMiscEntity transaction);
}
