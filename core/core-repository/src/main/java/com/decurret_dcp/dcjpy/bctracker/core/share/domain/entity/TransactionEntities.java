package com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 取引履歴関連のエンティティをまとめたクラス。
 */
@ToString
@EqualsAndHashCode
@Builder
public class TransactionEntities {

    public final TransactionEntity transaction;

    public final TransactionSenderEntity transactionSender;

    public final TransactionMiscEntity transactionMisc;

    public final TransactionMemoEntity transactionMemo;
}
