package com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity;

import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;
import org.seasar.doma.jdbc.entity.NamingType;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionId;

import lombok.Builder;

@Entity(immutable = true, naming = NamingType.SNAKE_LOWER_CASE)
@Table(name = "transaction_memo")
@Builder(toBuilder = true)
public class TransactionMemoEntity {

    /** トランザクションId */
    @Id
    @Column(name = "transaction_id")
    public final TransactionId transactionId;

    /** 摘要 */
    @Column(name = "memo")
    public final String memo;
}
