package com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity;

import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;
import org.seasar.doma.jdbc.entity.NamingType;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.MiscValue;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionId;

import lombok.Builder;

@Entity(immutable = true, naming = NamingType.SNAKE_LOWER_CASE)
@Table(name = "transaction_misc")
@Builder(toBuilder = true)
public class TransactionMiscEntity {

    /** トランザクションID */
    @Id
    @Column(name = "transaction_id")
    public final TransactionId transactionId;

    /** 汎用値1 */
    @Column(name = "misc_value1")
    public final MiscValue miscValue1;

    /** 汎用値2 */
    @Column(name = "misc_value2")
    public final MiscValue miscValue2;
}
