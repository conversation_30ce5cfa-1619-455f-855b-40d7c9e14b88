package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.validator;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.BCClientCallRequest;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.BCClientSupport;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.validator.response.ValidatorGetAccountResponse;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.ContractName;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.ContractValue;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.ValidatorApi;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.command.ValidatorGetAccountCommand;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.value.ContractBytes32Value;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class BCClientValidatorAdaptor implements ValidatorApi {

    private final BCClientSupport support;

    @Override
    public Either<ValidatorGetAccountResult> getAccount(ValidatorGetAccountCommand command) {
        Map<String, ContractValue> parameters = Map.ofEntries(
                Map.entry("validatorId", new ContractBytes32Value(command.validatorId.getValue())),
                Map.entry("accountId", new ContractBytes32Value(command.accountId.getValue()))
        );
        BCClientCallRequest request = BCClientCallRequest.create(
                command.zoneId, ContractName.VALIDATOR, "getAccount", parameters);

        Either<ValidatorGetAccountResponse> either = this.support.call(request, ValidatorGetAccountResponse.class);

        return either.map(success -> success.toResult());
    }
}
