rootProject.name = 'dcbg-dcjpy-bctracker'
include 'bctracker-base'
include 'bpm:bpm-repository'
findProject(':bpm:bpm-repository')?.name = 'bpm-repository'
include 'bpm:email-send-tracker'
findProject(':bpm:email-send-tracker')?.name = 'email-send-tracker'
include 'bpm:push-notification-tracker'
findProject(':bpm:push-notification-tracker')?.name = 'push-notification-tracker'
include 'core:core-repository'
findProject(':core:core-repository')?.name = 'core-repository'
include 'core:balance-tracker'
findProject(':core:balance-tracker')?.name = 'balance-tracker'
include 'core:transaction-tracker'
findProject(':core:transaction-tracker')?.name = 'transaction-tracker'
include 'core:invoke-core-tracker'
findProject(':core:invoke-core-tracker')?.name = 'invoke-core-tracker'

