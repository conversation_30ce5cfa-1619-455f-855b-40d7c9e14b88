package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class SyncBusinessZoneStatusEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final ZoneId businessZoneId;

    public final String businessZoneName;

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final AccountStatus accountStatus;

    public static SyncBusinessZoneStatusEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.SYNC_BUSINESS_ZONE_STATUS) {
            return null;
        }

        int businessZoneId = bcEvent.nonIndexedValues.get("zoneId").asInt();

        String validatorId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("validatorId"));
        String accountId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("accountId"));
        String businessZoneName = bcEvent.nonIndexedValues.get("zoneName").textValue();
        String accountStatus = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("accountStatus"));

        return SyncBusinessZoneStatusEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .businessZoneId(ZoneId.of(businessZoneId))
                .businessZoneName(businessZoneName)
                .validatorId(ValidatorId.of(validatorId))
                .accountId(AccountId.of(accountId))
                .accountStatus(AccountStatus.of(accountStatus))
                .build();
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.SYNC_BUSINESS_ZONE_STATUS;
    }
}
