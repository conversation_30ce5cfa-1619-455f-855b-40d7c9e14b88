package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

public enum UserType {

    /** 個人ユーザ / 法人ユーザ。 */
    DC_USER(OwnerType.USER),

    /** 銀行ユーザ / 事業者ユーザ。 */
    SERVICE_USER(OwnerType.SERVICE);

    private final OwnerType ownerType;

    private UserType(OwnerType ownerType) {
        this.ownerType = ownerType;
    }

    public OwnerType toOwnerType() {
        return this.ownerType;
    }
}
