package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * エンティティ種別を扱う。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum EntityType {

    /** admin : アドミニストレータ。 */
    ADMIN("admin"),

    /** provider : プロバイダ。 */
    PROVIDER("provider"),

    /** validator : バリデータ。 */
    VALIDATOR("validator"),

    /** issuer : イシュア。 */
    ISSUER("issuer");

    private static final Map<String, EntityType> OBJECT_MAP = Arrays.stream(EntityType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private EntityType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static EntityType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
