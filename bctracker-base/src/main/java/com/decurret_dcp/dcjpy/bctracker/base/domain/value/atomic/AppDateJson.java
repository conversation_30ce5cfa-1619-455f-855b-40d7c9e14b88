package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.io.IOException;
import java.time.LocalDate;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class AppDateJson {

    static class Serializer extends JsonSerializer<AppDate> {

        @Override
        public void serialize(AppDate value, JsonGenerator generator, SerializerProvider provider)
                throws IOException {
            if (value == null) {
                return;
            }

            generator.writeString(value.localDate().toString());
        }
    }

    static class Deserializer extends JsonDeserializer<AppDate> {

        @Override
        public AppDate deserialize(<PERSON><PERSON><PERSON><PERSON><PERSON> parser, DeserializationContext context) throws IOException {
            LocalDate localDate = LocalDate.parse(parser.getValueAsString());
            return AppDate.of(localDate);
        }
    }
}
