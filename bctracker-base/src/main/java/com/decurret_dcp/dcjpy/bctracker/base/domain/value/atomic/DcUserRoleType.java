package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 個人ユーザおよび法人ユーザのロール種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum DcUserRoleType {

    /** individual : 個人ユーザロール。 */
    INDIVIDUAL("individual", Integer.MAX_VALUE),

    /** account_owner : アカウント管理者。 */
    ACCOUNT_OWNER("account_owner", 9),

    /** user_owner : ユーザ管理者。 */
    USER_OWNER("user_owner", 5),

    /** operator : 業務担当。 */
    OPERATOR("operator", 1),

    /** reviewer : 業務承認。 */
    REVIEWER("reviewer", 1);

    private static final Map<String, DcUserRoleType> OBJECT_MAP = Arrays.stream(DcUserRoleType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final int level;

    private DcUserRoleType(String value, int level) {
        this.value = value;
        this.level = level;
    }

    public String getValue() {
        return this.value;
    }

    @Deprecated
    public int getLevel() {
        return this.level;
    }

    public static DcUserRoleType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }

    public boolean isHigherThan(DcUserRoleType other) {
        return this.level > other.level;
    }

    public Set<DcUserRoleType> lowerRoles() {
        return Arrays.stream(DcUserRoleType.values())
                .filter(type -> this.isHigherThan(type))
                .collect(Collectors.toSet());
    }
}
