package com.decurret_dcp.dcjpy.bctracker.base;

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

/**
 * BCTracker起動クラス.
 */
@RequiredArgsConstructor
@Component
public class BCTrackerRunner<ENTITY, MESSAGE> implements ApplicationRunner {

    /**
     * SQSハンドラー.
     */
    private final BCTrackerApplication<ENTITY, MESSAGE> application;

    /**
     * BCTracker起動.
     *
     * @param args 起動引数.
     */
    @Override
    public void run(ApplicationArguments args) {
        this.application.execute();
    }
}
