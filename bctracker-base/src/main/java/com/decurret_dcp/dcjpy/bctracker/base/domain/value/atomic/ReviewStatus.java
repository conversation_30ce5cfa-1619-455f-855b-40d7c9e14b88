package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum ReviewStatus {

    /** approval: 承認。 */
    APPROVAL("approval"),

    /** rejected: 否認。 */
    REJECTED("rejected");

    private static final Map<String, ReviewStatus> OBJECT_MAP = Arrays.stream(ReviewStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private ReviewStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static ReviewStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
