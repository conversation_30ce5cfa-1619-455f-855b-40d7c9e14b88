package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class SyncBusinessZoneBalanceEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final ZoneId zoneId;

    public final ValidatorId fromValidatorId;

    public final AccountId fromAccountId;

    public final String fromAccountName;

    public final Balance fromAccountBalance;

    public final ValidatorId toValidatorId;

    public final AccountId toAccountId;

    public final String toAccountName;

    public final Amount amount;

    public final Balance toAccountBalance;

    public static SyncBusinessZoneBalanceEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.SYNC_BUSINESS_ZONE_BALANCE) {
            return null;
        }

        JsonNode transferData = bcEvent.nonIndexedValues.get("transferData");

        ZoneId bizZoneId = ZoneId.of(transferData.get("zoneId").asInt());

        String fromValidatorId = JsonNodeReader.bytesToString(transferData.get("fromValidatorId"));
        String toValidatorId = JsonNodeReader.bytesToString(transferData.get("toValidatorId"));
        BigInteger fromAccountBalance = new BigInteger(transferData.get("fromAccountBalance").asText());
        BigInteger toAccountBalance = new BigInteger(transferData.get("toAccountBalance").asText());

        AccountId fromAccountId = AccountId.of(JsonNodeReader.bytesToString(transferData.get("fromAccountId")));
        String fromAccountName = (transferData.get("fromAccountName") != null) ?
                transferData.get("fromAccountName").textValue() : null;

        AccountId toAccountId = AccountId.of(JsonNodeReader.bytesToString(transferData.get("toAccountId")));
        String toAccountName = (transferData.get("toAccountName") != null) ?
                transferData.get("toAccountName").textValue() : null;

        BigInteger amount = new BigInteger(transferData.get("amount").asText());

        return SyncBusinessZoneBalanceEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .zoneId(bizZoneId)
                .fromValidatorId(ValidatorId.of(fromValidatorId))
                .fromAccountId(fromAccountId)
                .fromAccountName(fromAccountName)
                .fromAccountBalance(Balance.of(fromAccountBalance))
                .toValidatorId(ValidatorId.of(toValidatorId))
                .toAccountId(toAccountId)
                .toAccountName(toAccountName)
                .amount(Amount.of(amount))
                .toAccountBalance(Balance.of(toAccountBalance))
                .build();
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.SYNC_BUSINESS_ZONE_BALANCE;
    }
}
