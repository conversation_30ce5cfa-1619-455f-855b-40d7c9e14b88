package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.util.ArrayList;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
@Slf4j
public class ModTokenLimitEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public static ModTokenLimitEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.MOD_TOKEN_LIMIT) {
            return null;
        }

        String validatorId = JsonNodeReader.bytesToString(bcEvent.indexedValues.get("validatorId"));
        String accountId = JsonNodeReader.bytesToString(bcEvent.indexedValues.get("accountId"));
        boolean changed = hasChanged(bcEvent.nonIndexedValues);
        if (changed == false) {
            return null;
        }

        // コントラクトから伝搬される ModTokenLimit イベントには limitAmounts も含まれるが、
        // 利用していないため取得していない。

        return ModTokenLimitEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .validatorId(ValidatorId.of(validatorId))
                .accountId(AccountId.of(accountId))
                .build();
    }

    private static boolean hasChanged(JsonNode nonIndexedValues) {
        List<Boolean> itemFlags = new ArrayList<>(5);
        for (JsonNode item : nonIndexedValues.get("itemFlgs")) {
            itemFlags.add(Boolean.valueOf(item.asBoolean()));
        }

        if ((itemFlags.size() != 5)) {
            log.error("Invalid amount limit array length. {}", nonIndexedValues);
            return false;
        }

        // いずれか一つの項目がtrueであること
        return itemFlags.stream().anyMatch(item -> item.booleanValue() == true);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.MOD_TOKEN_LIMIT;
    }
}
