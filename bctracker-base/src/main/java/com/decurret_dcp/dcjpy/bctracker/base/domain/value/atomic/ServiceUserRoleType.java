package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 銀行ユーザおよび事業者ユーザのロール種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum ServiceUserRoleType {

    /** service_owner : サービス管理者。 */
    SERVICE_OWNER("service_owner", 9),

    /** user_owner : ユーザ管理者。 */
    USER_OWNER("user_owner", 5),

    /** operator : 業務担当。 */
    OPERATOR("operator", 1),

    /** reviewer : 業務承認。 */
    REVIEWER("reviewer", 1);

    private static final Map<String, ServiceUserRoleType> OBJECT_MAP = Arrays.stream(ServiceUserRoleType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final int level;

    private ServiceUserRoleType(String value, int level) {
        this.value = value;
        this.level = level;
    }

    public String getValue() {
        return this.value;
    }

    @Deprecated
    public int getLevel() {
        return this.level;
    }

    public static ServiceUserRoleType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }

    public boolean isHigherThan(ServiceUserRoleType other) {
        return this.level > other.level;
    }

    public Set<ServiceUserRoleType> lowerRoles() {
        return Arrays.stream(ServiceUserRoleType.values())
                .filter(type -> this.isHigherThan(type))
                .collect(Collectors.toSet());
    }
}
