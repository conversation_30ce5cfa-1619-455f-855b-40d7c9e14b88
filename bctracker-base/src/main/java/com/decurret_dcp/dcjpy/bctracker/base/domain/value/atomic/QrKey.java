package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class QrKey {

    private static final String PREFIX_QR_TOKEN = "DCJPY";

    public final String value;

    private QrKey(String value) {
        this.value = value;
    }

    public static QrKey of(String qrKey) {
        return new QrKey(qrKey);
    }

    public String getValue() {
        return this.value;
    }

    public String withPrefix() {
        return PREFIX_QR_TOKEN + this.value;
    }

    public static QrKey fromQrToken(String qrToken) {
        if (qrToken.startsWith(PREFIX_QR_TOKEN) == false) {
            throw new IllegalArgumentException("qrToken must be start with " + PREFIX_QR_TOKEN + ".");
        }

        String qrKey = qrToken.substring(PREFIX_QR_TOKEN.length());
        return QrKey.of(qrKey);
    }
}
