package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum FileTaskStatus {

    /** initialized : 初回処理待ち。 */
    INITIALIZED("initialized"),

    /** accept : 受付済み。 */
    ACCEPT("accept"),

    /** format_failure : フォーマット不備。 */
    FORMAT_FAILURE("format_failure"),

    /** in_processing : 処理実行中。 */
    IN_PROCESSING("in_processing"),

    /** completed : 処理完了。 */
    COMPLETED("completed"),

    /** failed : 処理失敗。 */
    FAILED("failed");

    private static final Map<String, FileTaskStatus> OBJECT_MAP = Arrays.stream(FileTaskStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private FileTaskStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static FileTaskStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
