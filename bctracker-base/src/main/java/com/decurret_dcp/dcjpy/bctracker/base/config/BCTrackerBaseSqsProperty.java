package com.decurret_dcp.dcjpy.bctracker.base.config;

import lombok.AllArgsConstructor;
import lombok.ToString;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.util.StringUtils;

/**
 * BCTrackerのプロパティー情報.
 */
@ConstructorBinding
@ConfigurationProperties(prefix = "bctracker.base.sqs")
@AllArgsConstructor
@ToString
public class BCTrackerBaseSqsProperty {

    /** 扱うイベントが格納されるキュー名。 */
    public final String queueName;

    /** 可視性タイムアウト時間 (秒). */
    public final Integer visibilityTimeout;

    /** ポーリングメッセージ待機時間 (秒). */
    public final Integer waitTimeSeconds;

    /** ローカル環境への接続先。 */
    public final String localEndpoint;

    /**
     * AWS 環境の SQS に接続するかどうか。
     *
     * @return AWS 環境の SQS に接続する場合 true
     */
    public boolean toAwsConnection() {
        return (StringUtils.hasText(this.localEndpoint) == false);
    }
}
