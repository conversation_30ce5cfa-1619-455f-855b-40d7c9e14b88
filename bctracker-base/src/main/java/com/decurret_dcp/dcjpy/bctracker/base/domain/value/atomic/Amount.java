package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.math.BigInteger;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 金額を表すクラス。
 */
@ToString
@EqualsAndHashCode
@JsonSerialize(using = AmountJson.Serializer.class)
@JsonDeserialize(using = AmountJson.Deserializer.class)
@Domain(valueType = BigInteger.class, factoryMethod = "of", acceptNull = false)
public class Amount {

    private final BigInteger value;

    private Amount(BigInteger value) {
        this.value = value;
    }

    public static Amount of(BigInteger value) {
        return new Amount(value);
    }

    public BigInteger getValue() {
        return this.value;
    }

    public boolean isZero() {
        return this.value.equals(BigInteger.ZERO);
    }
}
