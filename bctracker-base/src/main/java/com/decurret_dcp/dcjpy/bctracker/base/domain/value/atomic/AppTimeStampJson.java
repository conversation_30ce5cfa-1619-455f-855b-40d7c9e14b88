package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class AppTimeStampJson {

    static class Serializer extends JsonSerializer<AppTimeStamp> {

        @Override
        public void serialize(AppTimeStamp value, JsonGenerator generator, SerializerProvider provider)
                throws IOException {

            if (value == null) {
                return;
            }

            generator.writeString(value.zonedDateTime().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        }
    }

    static class Deserializer extends JsonDeserializer<AppTimeStamp> {

        @Override
        public AppTimeStamp deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            ZonedDateTime dateTime = ZonedDateTime.parse(
                    parser.getValueAsString(), DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            return AppTimeStamp.of(dateTime);
        }
    }
}
