package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * QR手続きフロー種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum QrFlowType {

    /** confirmation : 確認フロー。 */
    CONFIRMATION("confirmation"),

    /** order : 申請フロー。 */
    ORDER("order"),

    /** sign_in : サインイン。 */
    SIGN_IN("sign_in");

    private static final Map<String, QrFlowType> OBJECT_MAP = Arrays.stream(QrFlowType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private QrFlowType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static QrFlowType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
