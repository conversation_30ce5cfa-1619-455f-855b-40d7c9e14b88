package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;

/**
 * Nullオブジェクト
 */
public class NotSupportEvent implements BCEventTypeHolder {

    private static final NotSupportEvent INSTANCE = new NotSupportEvent();

    private NotSupportEvent() {
        // Do nothing.
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.NOT_SUPPORT;
    }

    public static NotSupportEvent create() {
        return INSTANCE;
    }
}
