package com.decurret_dcp.dcjpy.bctracker.base.domain.value;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.BurnEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.DischargeRequestedEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ForceBurnEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.IssueVoucherEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.MintEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ModTokenLimitEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.NotSupportEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.RedeemVoucherEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneBalanceEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneStatusEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class BCEvent<ORIGINAL> {

    public final String name;

    public final TransactionHash transactionHash;

    public final BlockTimeStamp blockTimestamp;

    public final long logIndex;

    public final String log;

    public final JsonNode indexedValues;

    public final JsonNode nonIndexedValues;

    private final ORIGINAL original;

    public ORIGINAL original() {
        return this.original;
    }

    public String traceId() {
        if (this.nonIndexedValues.has("traceId") == false) {
            return null;
        }

        JsonNode hexedTraceId = this.nonIndexedValues.get("traceId");
        return JsonNodeReader.bytesToString(hexedTraceId);
    }

    public BCEventTypeHolder to() {
        BCEventType eventType = BCEventType.of(this.name);
        return switch (eventType) {
            case MINT -> MintEvent.create(this);
            case BURN -> BurnEvent.create(this);
            case TRANSFER -> TransferEvent.create(this);
            case ISSUE_VOUCHER -> IssueVoucherEvent.create(this);
            case REDEEM_VOUCHER -> RedeemVoucherEvent.create(this);
            case FORCE_BURN -> ForceBurnEvent.create(this);
            case SYNC_BUSINESS_ZONE_STATUS -> SyncBusinessZoneStatusEvent.create(this);
            case SYNC_BUSINESS_ZONE_BALANCE -> SyncBusinessZoneBalanceEvent.create(this);
            case MOD_TOKEN_LIMIT -> ModTokenLimitEvent.create(this);
            case DISCHARGE_REQUESTED -> DischargeRequestedEvent.create(this);
            case NOT_SUPPORT -> NotSupportEvent.create();
        };
    }
}
