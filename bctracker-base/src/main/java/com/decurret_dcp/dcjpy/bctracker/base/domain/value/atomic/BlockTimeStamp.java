package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.concurrent.TimeUnit;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class BlockTimeStamp {

    private static final ZoneOffset TOKYO_ZONE_OFFSET = ZoneOffset.ofHours(9);

    private final long value;

    private BlockTimeStamp() {
        this(System.currentTimeMillis());
    }

    private BlockTimeStamp(long blockTimestamp) {
        this.value = blockTimestamp;
    }

    /**
     * 現在日時を返す。
     *
     * @return ExpiresAt オブジェクト
     */
    public static BlockTimeStamp now() {
        return new BlockTimeStamp();
    }

    public static BlockTimeStamp of(long blockTimestamp) {
        return new BlockTimeStamp(blockTimestamp);
    }

    public AppTimeStamp appTimeStamp() {
        //ミリ秒へ変換して渡す
        return AppTimeStamp.of(this.value * 1000);
    }

    /**
     * タイムゾーン付き日時を取得する。
     *
     * @return zoned DateTime
     */
    public ZonedDateTime zonedDateTime() {
        return this.zonedDateTime(TOKYO_ZONE_OFFSET);
    }

    /**
     * タイムゾーン付き日時を取得する。
     *
     * @param zoneId ZoneID
     *
     * @return zoned DateTime
     */
    public ZonedDateTime zonedDateTime(ZoneId zoneId) {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(this.value), zoneId);
    }

    public BlockTimeStamp after(long time, TimeUnit timeUnit) {
        long nextTimestamp = this.value + timeUnit.toMillis(time);
        return new BlockTimeStamp(nextTimestamp);
    }

    /**
     * 自身が引数に指定された日時より過去ならばtrue.
     *
     * @param other 比較対象
     *
     * @return 自身が引数に指定された日時より過去ならばtrue
     */
    public boolean isBeforeFrom(BlockTimeStamp other) {
        return (this.value < other.value);
    }

    /**
     * 自身が引数に指定された日時より未来ならばtrue.
     *
     * @param other 比較対象
     *
     * @return 自身が引数に指定された日時より未来ならばtrue
     */
    public boolean isAfterTo(BlockTimeStamp other) {
        return (this.value > other.value);
    }
}
