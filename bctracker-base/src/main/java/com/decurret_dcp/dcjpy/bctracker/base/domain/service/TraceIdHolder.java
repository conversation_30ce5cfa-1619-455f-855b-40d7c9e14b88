package com.decurret_dcp.dcjpy.bctracker.base.domain.service;

import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TraceId;

public class TraceIdHolder {

    private static final ThreadLocal<TraceId> TL_TRACE_ID = new ThreadLocal<TraceId>();

    public static TraceId getTraceId() {
        return TL_TRACE_ID.get();
    }

    public static void setTraceId(String value) {
        if (StringUtils.hasText(value) == false) {
            return;
        }

        TL_TRACE_ID.set(TraceId.of(value));
        MDC.put("traceId", value);
    }

    public static void remove() {
        TL_TRACE_ID.remove();
        MDC.remove("traceId");
    }
}
