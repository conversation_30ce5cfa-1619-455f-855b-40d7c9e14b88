package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 個人ユーザおよび法人ユーザの権限を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum DcUserAuthorityKey {

    /** mint : DCJPY 発行。 */
    MINT("mint", true, false),

    /** burn : DCJPY 償却。 */
    BURN("burn", true, false),

    /** transfer : DCJPY 送金。 */
    TRANSFER("transfer", true, true),

    /** charge : DCJPY チャージ / DCJPY ディスチャージ。 */
    CHARGE("charge", true, true),

    /** view_account : アカウント 情報照会。 */
    VIEW_ACCOUNT("view_account", true, true),

    /** view_transaction : アカウント 取引履歴照会。 */
    VIEW_TRANSACTIONS("view_transactions", true, true),

    /** change_account : アカウント 情報変更。 */
    CHANGE_ACCOUNT("change_account", true, true),

    /** view_information : インフォメーション閲覧。 */
    VIEW_INFORMATION("view_information", true, true);

    private static final Map<String, DcUserAuthorityKey> OBJECT_MAP = Arrays.stream(DcUserAuthorityKey.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private static final List<DcUserAuthorityKey> FIN_ZONE_AVAILABLE_LIST = Arrays.stream(DcUserAuthorityKey.values())
            .filter(key -> key.finZoneAvailable == true).toList();

    private static final List<DcUserAuthorityKey> BIZ_ZONE_AVAILABLE_LIST = Arrays.stream(DcUserAuthorityKey.values())
            .filter(key -> key.bizZoneAvailable == true).toList();

    private final String value;

    private final boolean finZoneAvailable;

    private final boolean bizZoneAvailable;

    private DcUserAuthorityKey(String value, boolean finZoneAvailable, boolean bizZoneAvailable) {
        this.value = value;
        this.finZoneAvailable = finZoneAvailable;
        this.bizZoneAvailable = bizZoneAvailable;
    }

    public String getValue() {
        return this.value;
    }

    public static DcUserAuthorityKey of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }

    public static List<DcUserAuthorityKey> availableInFinZone() {
        return FIN_ZONE_AVAILABLE_LIST;
    }

    public static List<DcUserAuthorityKey> availableInBizZone() {
        return BIZ_ZONE_AVAILABLE_LIST;
    }
}
