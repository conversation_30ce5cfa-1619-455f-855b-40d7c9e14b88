package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class RedeemVoucherEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AccountId accountId;

    public final String accountName;

    public final AppTimeStamp blockTimeStamp;

    public final ZoneId zoneId;

    public final ValidatorId validatorId;

    public final Amount amount;

    public final Balance balance;

    public static RedeemVoucherEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.REDEEM_VOUCHER) {
            return null;
        }

        int zoneId = bcEvent.indexedValues.get("zoneId").asInt();

        String validatorId = JsonNodeReader.bytesToString(bcEvent.indexedValues.get("validatorId"));
        String accountId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("accountId"));
        String accountName = (bcEvent.nonIndexedValues.get("accountName") != null) ?
                bcEvent.nonIndexedValues.get("accountName").textValue() : null;

        BigInteger amount = new BigInteger(bcEvent.nonIndexedValues.get("amount").asText());
        BigInteger balance = new BigInteger(bcEvent.nonIndexedValues.get("balance").asText());

        return RedeemVoucherEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .accountId(AccountId.of(accountId))
                .accountName(accountName)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .zoneId(ZoneId.of(zoneId))
                .validatorId(ValidatorId.of(validatorId))
                .amount(Amount.of(amount))
                .balance(Balance.of(balance))
                .build();
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.REDEEM_VOUCHER;
    }
}
