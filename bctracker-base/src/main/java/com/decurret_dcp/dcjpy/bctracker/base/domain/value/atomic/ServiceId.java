package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;
import org.springframework.util.StringUtils;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class ServiceId {

    public static final ServiceId DEFAULT = new ServiceId("0");

    public final String value;

    private ServiceId(String value) {
        this.value = value;
    }

    public static ServiceId of(String serviceId) {
        if (StringUtils.hasText(serviceId) == false) {
            return DEFAULT;
        }

        String trimServiceId = serviceId.trim();
        if ("0".equalsIgnoreCase(trimServiceId)) {
            return DEFAULT;
        }

        return new ServiceId(trimServiceId);
    }

    public String getValue() {
        return this.value;
    }
}
