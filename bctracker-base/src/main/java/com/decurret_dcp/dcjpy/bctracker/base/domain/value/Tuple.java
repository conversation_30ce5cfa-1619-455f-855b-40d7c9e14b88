package com.decurret_dcp.dcjpy.bctracker.base.domain.value;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class Tuple<FIRST, SECOND> {

    public final FIRST first;

    public final SECOND second;

    private Tuple(FIRST first, SECOND second) {
        this.first = first;
        this.second = second;
    }

    public static <FIRST, SECOND> Tuple<FIRST, SECOND> of(FIRST first, SECOND second) {
        return new Tuple<>(first, second);
    }
}