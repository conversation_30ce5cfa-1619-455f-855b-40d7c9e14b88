package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.UUID;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class InformationId {

    public final UUID value;

    private InformationId(UUID value) {
        this.value = value;
    }

    public static InformationId of(String informationId) {
        UUID uuid = UUID.fromString(informationId);
        return new InformationId(uuid);
    }

    public static InformationId of(UUID informationId) {
        return new InformationId(informationId);
    }

    public static InformationId generate() {
        return new InformationId(UUID.randomUUID());
    }

    public String getValue() {
        return this.value.toString();
    }
}
