package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 申請状態を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum OrderStatus {

    /** pending : 手続き待ち。 */
    PENDING("pending", false),

    /** in_approving : 承認手続き中。 */
    IN_APPROVING("in_approving", false),

    /** approval : 承認。 */
    APPROVAL("approval", true),

    /** rejected : 否認。 */
    REJECTED("rejected", true);

    private static final Map<String, OrderStatus> OBJECT_MAP = Arrays.stream(OrderStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final boolean completed;

    private OrderStatus(String value, boolean completed) {
        this.value = value;
        this.completed = completed;
    }

    public String getValue() {
        return this.value;
    }

    public boolean isCompleted() {
        return this.completed;
    }

    public static OrderStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
