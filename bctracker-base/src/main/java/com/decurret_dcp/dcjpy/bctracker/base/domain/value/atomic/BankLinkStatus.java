package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * IB との接続状態を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum BankLinkStatus {

    /** authenticated : 認証済み。 */
    AUTHENTICATED("authenticated"),

    /** need_auth : 認証が必要。 */
    NEED_AUTH("need_auth");

    private static final Map<String, BankLinkStatus> OBJECT_MAP = Arrays.stream(BankLinkStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private BankLinkStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static BankLinkStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
