package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum NotificationType {


    /** order : 依頼通知 */
    ORDER("order", TemplateKey.ORDER) {
        @Override
        public DcUserRoleType toNotifier() {
            return DcUserRoleType.REVIEWER;
        }
    },

    /** order_complete : 承認通知 */
    ORDER_COMPLETED("order_completed", TemplateKey.ORDER_COMPLETED) {
        @Override
        public DcUserRoleType toNotifier() {
            return DcUserRoleType.REVIEWER;
        }
    },

    /** order_rejected : 依頼否認通知 */
    ORDER_REJECTED("order_rejected", TemplateKey.ORDER_REJECTED) {
        @Override
        public DcUserRoleType toNotifier() {
            return DcUserRoleType.REVIEWER;
        }
    },

    /** transfer : 送金通知 */
    TRANSFER("transfer", TemplateKey.TRANSFER) {
        @Override
        public DcUserRoleType toNotifier() {
            return DcUserRoleType.ACCOUNT_OWNER;
        }
    },

    /** account_updated : アカウント情報変更通知 */
    ACCOUNT_UPDATED("account_updated", TemplateKey.ACCOUNT_UPDATED) {
        @Override
        public DcUserRoleType toNotifier() {
            return DcUserRoleType.ACCOUNT_OWNER;
        }
    },

    /** sign_in : サインイン */
    SIGN_IN("sign_in", TemplateKey.SIGN_IN) {
        @Override
        public DcUserRoleType toNotifier() {
            return null;
        }
    },

    /** synchronous : ビジネスゾーンアカウント開設 */
    SYNCHRONOUS("synchronous", TemplateKey.BIZ_APPLYING) {
        @Override
        public DcUserRoleType toNotifier() {
            return DcUserRoleType.ACCOUNT_OWNER;
        }
    },

    /** biz_terminating:ビジネスゾーンアカウント解約 */
    BIZ_TERMINATING("biz_terminating", TemplateKey.BIZ_TERMINATING) {
        @Override
        public DcUserRoleType toNotifier() {
            return DcUserRoleType.ACCOUNT_OWNER;
        }
    };

    private final String value;

    private final TemplateKey templateKey;

    private NotificationType(String value) {
        this(value, null);
    }

    private NotificationType(String value, TemplateKey templateKey) {
        this.value = value;
        this.templateKey = templateKey;
    }

    public TemplateKey getTemplateKey() {
        if (this.templateKey == null) {
            throw new IllegalStateException("Not implement rejection. NotificationType : " + this.value);
        }

        return this.templateKey;
    }

    public abstract DcUserRoleType toNotifier();

    public String getValue() {
        return this.value;
    }

    public static NotificationType of(String value) {
        for (NotificationType type : NotificationType.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }

        return null;
    }
}
