package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class ProviderId {

    private final String value;

    private ProviderId(String value) {
        this.value = value;
    }

    public static ProviderId of(String accountId) {
        return new ProviderId(accountId);
    }

    public EntityId toEntityId() {
        return EntityId.of(this.value);
    }

    public String getValue() {
        return this.value;
    }
}
