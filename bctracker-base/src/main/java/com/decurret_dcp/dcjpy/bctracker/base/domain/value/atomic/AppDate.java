package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.time.LocalDate;
import java.time.ZoneOffset;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@JsonSerialize(using = AppDateJson.Serializer.class)
@JsonDeserialize(using = AppDateJson.Deserializer.class)
public class AppDate {

    private static final ZoneOffset TOKYO_ZONE_OFFSET = ZoneOffset.ofHours(9);

    private final LocalDate value;

    private AppDate(LocalDate timestamp) {
        this.value = timestamp;
    }

    /**
     * タイムゾーン付き日時を取得する。
     *
     * @return zoned DateTime
     */
    public LocalDate localDate() {
        return this.value;
    }

    /**
     * 自身が引数に指定された日時より過去ならばtrue.
     *
     * @param other 比較対象
     * @return 自身が引数に指定された日時より過去ならばtrue
     */
    public boolean isBeforeFrom(AppDate other) {
        return (this.value.toEpochDay() < other.value.toEpochDay());
    }

    /**
     * 自身が引数に指定された日時より未来ならばtrue.
     *
     * @param other 比較対象
     * @return 自身が引数に指定された日時より未来ならばtrue
     */
    public boolean isAfterTo(AppDate other) {
        return (this.value.toEpochDay() > other.value.toEpochDay());
    }

    /**
     * 現在日時を返す。
     *
     * @return ExpiresAt オブジェクト
     */
    public static AppDate now() {
        return new AppDate(LocalDate.now(TOKYO_ZONE_OFFSET));
    }

    /**
     * 指定された日時のオブジェクトを返す。
     *
     * @param datetime タイムゾーン付き日時
     * @return ExpiresAt オブジェクト
     */
    public static AppDate of(LocalDate datetime) {
        return new AppDate(datetime);
    }
}
