package com.decurret_dcp.dcjpy.bctracker.base.adaptor.db;

import java.util.function.Supplier;

import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.decurret_dcp.dcjpy.bctracker.base.domain.repository.TransactionSupport;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class DomaTransactionSupport implements TransactionSupport {

    private final PlatformTransactionManager transactionManager;

    @Override
    public <RESULT> RESULT transaction(Supplier<RESULT> supplier) {
        TransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = this.transactionManager.getTransaction(transactionDefinition);
        try {
            RESULT result = supplier.get();
            this.transactionManager.commit(transactionStatus);

            return result;
        } catch (RuntimeException exc) {
            this.transactionManager.rollback(transactionStatus);
            throw exc;
        }
    }
}
