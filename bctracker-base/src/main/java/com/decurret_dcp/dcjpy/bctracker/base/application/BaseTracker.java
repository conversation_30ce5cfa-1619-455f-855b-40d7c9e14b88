package com.decurret_dcp.dcjpy.bctracker.base.application;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;

/**
 * トラッカー機能の I/F.
 */
public interface BaseTracker<ENTITY> {

    /**
     * メッセージ処理を行う.
     *
     * @param message 処理対象メッセージ.
     *
     * @return 処理が完了した場合に限り true を返却
     */
    public boolean onMessage(ENTITY message);

    /**
     * 汎用的なデータ型を該当 tracker が処理できる型に変換する。
     * 引数に指定された event が処理対象外の場合は null を返すこと。
     *
     * @param event 受信イベント
     *
     * @return 変換後のイベント
     */
    public ENTITY acceptable(BCEvent<?> event);
}
