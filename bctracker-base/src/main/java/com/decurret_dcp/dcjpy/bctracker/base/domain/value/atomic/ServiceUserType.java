package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 銀行ユーザおよび事業者ユーザのユーザ種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum ServiceUserType {

    /** owner : 管理者。 */
    OWNER("owner"),

    /** normal : 通常ユーザ。 */
    NORMAL("normal");

    private static final Map<String, ServiceUserType> OBJECT_MAP = Arrays.stream(ServiceUserType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private ServiceUserType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static ServiceUserType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
