package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.apache.commons.codec.binary.Base32;
import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class TotpSecret {

    private static final Base32 BASE32_CODEC = new Base32();

    public final String value;

    private TotpSecret(String value) {
        this.value = value;
    }

    public static TotpSecret of(String totpSecret) {
        return new TotpSecret(totpSecret);
    }

    public String getValue() {
        return this.value;
    }
}
