package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;
import org.springframework.util.StringUtils;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class MiscValue {

    public final String value;

    private MiscValue(String value) {
        this.value = value;
    }

    public static MiscValue of(String state) {
        if (StringUtils.hasText(state) == false) {
            return null;
        }

        return new MiscValue(state.trim());
    }

    public String getValue() {
        return this.value;
    }
}
