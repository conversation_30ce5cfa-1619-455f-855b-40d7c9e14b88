package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class OperationDetail {

    private final QrConfirmationType confirmationType;

    private final String jsonString;

    private OperationDetail(QrConfirmationType confirmationType, String jsonString) {
        this.confirmationType = confirmationType;
        this.jsonString = jsonString;
    }

    public static OperationDetail of(OperationDetailContent content) {
        QrConfirmationType confirmationType = content.qrConfirmationType();
        String jsonString = JsonConverter.toStringValue(content);
        return new OperationDetail(confirmationType, jsonString);
    }

    public QrConfirmationType confirmationType() {
        return this.confirmationType;
    }

    public <TYPE extends OperationDetailContent> TYPE getContent(
            TypeReference<TYPE> typeReference, QrConfirmationType confirmationType
    ) {
        if (this.confirmationType() != confirmationType) {
            return null;
        }

        return JsonConverter.toJsonValue(this.jsonString, typeReference);
    }

    // Doma2 が利用する想定
    @Deprecated
    public static OperationDetail of(String jsonString) {
        JsonNode jsonNode = JsonConverter.toJsonNode(jsonString);
        String confirmationType = jsonNode.get(QrConfirmationType.KEY_NAME).asText();
        return new OperationDetail(QrConfirmationType.of(confirmationType), jsonString);
    }

    // Doma2 が利用する想定
    @Deprecated
    public String getValue() {
        return this.jsonString;
    }
}
