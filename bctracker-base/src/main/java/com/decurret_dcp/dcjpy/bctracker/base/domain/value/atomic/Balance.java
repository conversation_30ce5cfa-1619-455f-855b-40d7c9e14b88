package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.math.BigInteger;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.Value;

/**
 * 残高を表すクラス。
 */
@Domain(valueType = BigInteger.class)
@Value
@JsonSerialize(using = BalanceJson.Serializer.class)
@JsonDeserialize(using = BalanceJson.Deserializer.class)
public class Balance {

    public static final Balance ZERO = new Balance(BigInteger.valueOf(0L));

    public final BigInteger value;

    public Balance(BigInteger value) {
        this.value = value;
    }

    public static Balance of(BigInteger value) {
        return new Balance(value);
    }
}
