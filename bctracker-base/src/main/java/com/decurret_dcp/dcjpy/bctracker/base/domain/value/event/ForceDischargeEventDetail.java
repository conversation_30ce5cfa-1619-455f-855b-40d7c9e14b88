package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class ForceDischargeEventDetail {

    /** ゾーンID */
    public final ZoneId zoneId;

    /** ディスチャージ金額 **/
    public final Amount dischargeAmount;
}
