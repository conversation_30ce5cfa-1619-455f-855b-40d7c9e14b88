package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.decurret_dcp.dcjpy.bctracker.base.domain.UUIDv7;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class TransactionId {

    public final String value;

    private TransactionId(String value) {
        this.value = value;
    }

    public static TransactionId of(String transactionId) {
        return new TransactionId(transactionId);
    }

    public static TransactionId generate() {
        return of(UUIDv7.create().toString());
    }

    public String getValue() {
        return this.value;
    }
}
