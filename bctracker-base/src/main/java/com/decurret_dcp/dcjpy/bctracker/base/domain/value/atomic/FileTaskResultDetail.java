package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.core.type.TypeReference;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class FileTaskResultDetail {

    private final String jsonString;

    private FileTaskResultDetail(String jsonString) {
        this.jsonString = jsonString;
    }

    public static FileTaskResultDetail of(Object jsonValue) {
        String jsonString = JsonConverter.toStringValue(jsonValue);
        return new FileTaskResultDetail(jsonString);
    }

    // Doma2 が利用する想定
    @Deprecated
    public static FileTaskResultDetail of(String jsonString) {
        return new FileTaskResultDetail(jsonString);
    }

    public <TYPE extends FileTaskResultDetailContent> TYPE getContent(TypeReference<TYPE> typeReference) {
        return JsonConverter.toJsonValue(this.jsonString, typeReference);
    }

    // Doma2 が利用する想定
    @Deprecated
    public String getValue() {
        return this.jsonString;
    }
}
