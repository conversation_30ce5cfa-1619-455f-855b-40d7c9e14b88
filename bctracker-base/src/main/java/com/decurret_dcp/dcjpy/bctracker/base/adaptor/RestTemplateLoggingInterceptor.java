package com.decurret_dcp.dcjpy.bctracker.base.adaptor;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.util.StreamUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RestTemplateLoggingInterceptor implements ClientHttpRequestInterceptor {

    private final Set<String> MASK_PARAMETERS = Set.of(
            "accountName",
            "accountNames",
            "fromAccountName",
            "toAccountName",
            "spenderAccountName",
            "accountSignature",
            "signature",
            "info",
            "memo",
            "stateCode"
    );

    private static final ObjectMapper JSON_MAPPER = new ObjectMapper();

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
        if (log.isInfoEnabled() == false) {
            return execution.execute(request, body);
        }

        HttpMethod httpMethod = request.getMethod();
        URI requestUri = request.getURI();

        log.info("Send request: [{} {}, headers=[{}], payload={}]",
                 (httpMethod != null) ? httpMethod.name() : "",
                 requestUri,
                 request.getHeaders(),
                 this.toMaskedValue(new String(body))
        );

        ClientHttpResponse baseResponse = execution.execute(request, body);
        ClientHttpResponse wrapper = new BufferingClientHttpResponseWrapper(baseResponse);

        HttpStatus status = HttpStatus.resolve(wrapper.getRawStatusCode());
        String responsePayload = this.readResponseBody(wrapper);

        log.info("Receive response: [{} {}, status={}({}), payload={}]",
                 (httpMethod != null) ? httpMethod.name() : "",
                 requestUri,
                 Integer.valueOf((status != null) ? status.value() : wrapper.getRawStatusCode()),
                 (status != null) ? status.getReasonPhrase() : "????",
                 (responsePayload != null) ? this.toMaskedValue(responsePayload) : "null"
        );

        return wrapper;
    }

    private String readResponseBody(ClientHttpResponse wrapper) {
        StringBuilder builder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(wrapper.getBody(), StandardCharsets.UTF_8)
        )) {
            for (String readLine = reader.readLine(); readLine != null; readLine = reader.readLine()) {
                builder.append(readLine);
            }
        } catch (IOException ioExc) {
            return null;
        }

        return builder.toString();
    }

    /**
     * Same toMaskedValue function as in BpmAccessLoggingFilter.java
     *
     * @param payload string
     *
     * @return maskedPayload
     */
    private String toMaskedValue(String payload) {
        JsonNode jsonNode;
        try {
            jsonNode = JSON_MAPPER.readTree(payload);
        } catch (IOException ioExc) {
            log.debug("Failed to parse body to json. {}", ioExc.getMessage());
            return payload;
        }

        this.maskValues(jsonNode);

        String maskedPayload;
        try {
            maskedPayload = JSON_MAPPER.writeValueAsString(jsonNode);
        } catch (JsonProcessingException jsonExc) {
            log.debug("Failed to decode json. {}", jsonExc.getMessage());
            return payload;
        }

        return maskedPayload;
    }

    /**
     * Same maskValues function as in BpmAccessLoggingFilter.java
     *
     * @param jsonNode JsonNode
     */
    private void maskValues(JsonNode jsonNode) {
        if (jsonNode.isObject() == false) {
            return;
        }

        jsonNode.fields().forEachRemaining(entry -> {
            String parameterName = entry.getKey();
            if (MASK_PARAMETERS.contains(parameterName)) {
                // `jsonNode.isObject() == true` が成り立っているので、以下のキャストは成功する
                ((ObjectNode) jsonNode).put(parameterName, "[**MASKED**]");
                return;
            }

            JsonNode subNode = entry.getValue();
            if (subNode.isObject()) {
                this.maskValues(subNode);
            }
            if (subNode.isArray()) {
                subNode.elements().forEachRemaining(subEntry -> this.maskValues(subEntry));
            }
        });
    }

    private static class BufferingClientHttpResponseWrapper implements ClientHttpResponse {

        private final ClientHttpResponse response;

        @Nullable
        private byte[] body;

        BufferingClientHttpResponseWrapper(ClientHttpResponse response) {
            this.response = response;
        }

        @Override
        public HttpStatus getStatusCode() throws IOException {
            return this.response.getStatusCode();
        }

        @Override
        public int getRawStatusCode() throws IOException {
            return this.response.getRawStatusCode();
        }

        @Override
        public String getStatusText() throws IOException {
            return this.response.getStatusText();
        }

        @Override
        public HttpHeaders getHeaders() {
            return this.response.getHeaders();
        }

        @Override
        public InputStream getBody() throws IOException {
            if (this.body == null) {
                this.body = StreamUtils.copyToByteArray(this.response.getBody());
            }
            return new ByteArrayInputStream(this.body);
        }

        @Override
        public void close() {
            this.response.close();
        }
    }
}
