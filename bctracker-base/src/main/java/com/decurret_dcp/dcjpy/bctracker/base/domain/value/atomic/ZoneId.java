package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@JsonSerialize(using = ZoneIdJson.Serializer.class)
@JsonDeserialize(using = ZoneIdJson.Deserializer.class)
@Domain(valueType = Integer.class, factoryMethod = "of", acceptNull = false)
public class ZoneId {

    private static final ZoneId FIN_ZONE_ID = ZoneId.of(3000);

    public final Integer value;

    private ZoneId(Integer value) {
        this.value = value;
    }

    public static ZoneId of(Integer zoneId) {
        return new ZoneId(zoneId);
    }

    public static ZoneId of(int zoneId) {
        return of(Integer.valueOf(zoneId));
    }

    public static ZoneId of(String zoneId) {
        return of(Integer.valueOf(zoneId));
    }

    public Integer getValue() {
        return this.value;
    }

    public static ZoneId financialZoneId() {
        return FIN_ZONE_ID;
    }
}
