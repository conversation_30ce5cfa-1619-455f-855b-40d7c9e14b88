package com.decurret_dcp.dcjpy.bctracker.base.domain.value;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 移転種別を表す値。
 */
public enum TransferEventType {

    /** 通常の移転。 */
    TRANSFER("transfer") {
        @Override
        public boolean isTransfer() {
            return true;
        }
    },

    /** カスタムトランスファー。 */
    CUSTOM_TRANSFER("custom_transfer") {
        @Override
        public boolean isTransfer() {
            return true;
        }
    },

    /** チャージ。 */
    CHARGE("charge"),

    /** ディスチャージ。 */
    DISCHARGE("discharge");

    private static final Map<String, TransferEventType> OBJECT_MAP = Arrays.stream(TransferEventType.values())
            .collect(Collectors.toMap(type -> type.getValue().toLowerCase(), type -> type));

    private final String value;

    private TransferEventType(String value) {
        this.value = value;
    }

    public static TransferEventType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }

    public boolean isTransfer() {
        return false;
    }

    public String getValue() {
        return this.value;
    }
}
