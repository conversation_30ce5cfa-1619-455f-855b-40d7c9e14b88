package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TraceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class ForceBurnEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final TraceId traceId;

    public final Amount burnedAmount;

    public final Balance burnedBalance;

    public final List<ForceDischargeEventDetail> forceDischargeEventDetails;

    public static ForceBurnEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.FORCE_BURN) {
            return null;
        }

        String validatorId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("validatorId"));
        String accountId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("accountId"));
        String traceId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("traceId"));
        BigInteger burnedAmount = new BigInteger(bcEvent.nonIndexedValues.get("burnedAmount").asText());
        BigInteger burnedBalance = new BigInteger(bcEvent.nonIndexedValues.get("burnedBalance").asText());
        List<ForceDischargeEventDetail> forceDischargeEvents = parseForceDischargeEvents(bcEvent.nonIndexedValues);

        return ForceBurnEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .validatorId(ValidatorId.of(validatorId))
                .accountId(AccountId.of(accountId))
                .traceId(TraceId.of(traceId))
                .burnedAmount(Amount.of(burnedAmount))
                .burnedBalance(Balance.of(burnedBalance))
                .forceDischargeEventDetails(forceDischargeEvents)
                .build();
    }

    private static List<ForceDischargeEventDetail> parseForceDischargeEvents(JsonNode nonIndexedValues) {
        JsonNode forceDischarge = nonIndexedValues.get("forceDischarge");
        if (forceDischarge == null) {
            return List.of();
        }

        List<ForceDischargeEventDetail> events = new ArrayList<>();
        for (JsonNode jsonNode : forceDischarge) {
            ZoneId zoneId = ZoneId.of(jsonNode.get("zoneId").asInt());
            BigInteger dischargeAmount = new BigInteger(jsonNode.get("dischargeAmount").asText());
            ForceDischargeEventDetail eventDetail = ForceDischargeEventDetail.builder()
                    .zoneId(zoneId)
                    .dischargeAmount(Amount.of(dischargeAmount))
                    .build();

            events.add(eventDetail);
        }

        return List.copyOf(events);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.FORCE_BURN;
    }
}
