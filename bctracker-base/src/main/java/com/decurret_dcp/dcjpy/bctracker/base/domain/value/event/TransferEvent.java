package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.math.BigInteger;

import org.apache.commons.lang3.StringUtils;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.TransferEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.MiscValue;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class TransferEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final TransferEventType transferType;

    public final ZoneId zoneId;

    public final ValidatorId fromValidatorId;

    public final ValidatorId toValidatorId;

    public final Balance fromAccountBalance;

    public final Balance toAccountBalance;

    public final Balance businessZoneBalance;

    public final ZoneId bizZoneId;

    public final AccountId sendAccountId;

    public final AccountId fromAccountId;

    public final String fromAccountName;

    public final AccountId toAccountId;

    public final String toAccountName;

    public final Amount amount;

    public final MiscValue miscValue1;

    public final MiscValue miscValue2;

    public final String memo;

    public static TransferEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.TRANSFER) {
            return null;
        }

        JsonNode transferData = bcEvent.nonIndexedValues.get("transferData");

        TransferEventType transferType = TransferEventType.of(
                JsonNodeReader.bytesToString(transferData.get("transferType")));
        ZoneId zoneId = ZoneId.of(transferData.get("zoneId").asInt());

        String fromValidatorId = JsonNodeReader.bytesToString(transferData.get("fromValidatorId"));
        String toValidatorId = JsonNodeReader.bytesToString(transferData.get("toValidatorId"));

        BigInteger fromAccountBalance = new BigInteger(transferData.get("fromAccountBalance").asText());
        BigInteger toAccountBalance = new BigInteger(transferData.get("toAccountBalance").asText());
        BigInteger businessZoneBalance = new BigInteger(transferData.get("businessZoneBalance").asText());

        ZoneId bizZoneId = ZoneId.of(transferData.get("bizZoneId").asInt());

        AccountId sendAccountId = AccountId.of(JsonNodeReader.bytesToString(transferData.get("sendAccountId")));
        AccountId fromAccountId = AccountId.of(JsonNodeReader.bytesToString(transferData.get("fromAccountId")));

        String fromAccountName = (transferData.get("fromAccountName") != null) ?
                transferData.get("fromAccountName").textValue() : null;

        AccountId toAccountId = AccountId.of(JsonNodeReader.bytesToString(transferData.get("toAccountId")));

        String toAccountName = (transferData.get("toAccountName") != null) ?
                transferData.get("toAccountName").textValue() : null;

        BigInteger amount = new BigInteger(transferData.get("amount").asText());

        String miscValue1 = getMiscValue(transferData, "miscValue1");
        String miscValue2 = (transferData.get("miscValue2") != null) ?
                transferData.get("miscValue2").textValue() : null;

        boolean isNullOrWhiteSpace = (transferData.get("memo") == null) ||
                StringUtils.isWhitespace(transferData.get("memo").textValue());
        String memo = isNullOrWhiteSpace ? null : transferData.get("memo").textValue();

        return TransferEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .transferType(transferType)
                .zoneId(zoneId)
                .fromValidatorId(ValidatorId.of(fromValidatorId))
                .toValidatorId(ValidatorId.of(toValidatorId))
                .fromAccountBalance(Balance.of(fromAccountBalance))
                .toAccountBalance(Balance.of(toAccountBalance))
                .businessZoneBalance(Balance.of(businessZoneBalance))
                .bizZoneId(bizZoneId)
                .sendAccountId(sendAccountId)
                .fromAccountId(fromAccountId)
                .fromAccountName(fromAccountName)
                .toAccountId(toAccountId)
                .toAccountName(toAccountName)
                .amount(Amount.of(amount))
                .miscValue1(MiscValue.of(miscValue1))
                .miscValue2(MiscValue.of(miscValue2))
                .memo(memo)
                .build();
    }

    private static String getMiscValue(JsonNode data, String name) {
        JsonNode miscNode = data.get(name);
        if ((miscNode == null) || miscNode.isNull()) {
            return null;
        }

        if (miscNode.isTextual() && miscNode.textValue().isEmpty()) {
            return null;
        }

        return JsonNodeReader.bytesToString(miscNode);
    }

    public ZoneId otherZoneId() {
        if (this.transferType.isTransfer()) {
            return this.zoneId;
        }

        return this.bizZoneId;
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.TRANSFER;
    }
}
