package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * mint 処理状態を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum MintStatus {

    /** initialize : 処理開始前。 */
    INITIALIZE("initialize"),

    /** withdrawn : 銀行預金口座から出金済み。 */
    WITHDRAWN("withdrawn"),

    /** withdraw_failed : 銀行預金口座から出金できなかった。 */
    WITHDRAW_FAILED("withdraw_failed"),

    /** minting_failed : 銀行預金口座から出金後、DCJPY 発行が失敗。 */
    MINTING_FAILED("minting_failed"),

    /** completed : DCJPY 発行手続きが正常に完了。 */
    COMPLETED("completed");

    private static final Map<String, MintStatus> OBJECT_MAP = Arrays.stream(MintStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private MintStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static MintStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
