package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.text.DecimalFormat;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = long.class, factoryMethod = "of", acceptNull = false)
public class OrderId {

    private static final ThreadLocal<DecimalFormat> TL_FORMAT =
            ThreadLocal.withInitial(() -> new DecimalFormat("000000000"));

    public final long value;

    private OrderId(long value) {
        this.value = value;
    }

    public static OrderId of(long orderId) {
        return new OrderId(orderId);
    }

    public static OrderId of(Long orderId) {
        return new OrderId(orderId.longValue());
    }

    public long getValue() {
        return this.value;
    }

    public String format() {
        DecimalFormat format = TL_FORMAT.get();
        return format.format(this.value);
    }
}
