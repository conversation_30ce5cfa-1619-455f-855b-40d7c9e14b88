package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class OrderDetail {

    private final String jsonString;

    private OrderDetail(String jsonString) {
        this.jsonString = jsonString;
    }

    public static OrderDetail of(String jsonString) {
        return new OrderDetail(jsonString);
    }

    public static OrderDetail of(Object jsonValue) {
        String jsonString = JsonConverter.toStringValue(jsonValue);
        return new OrderDetail(jsonString);
    }

    @Deprecated
    public String getValue() {
        return this.jsonString;
    }
}
