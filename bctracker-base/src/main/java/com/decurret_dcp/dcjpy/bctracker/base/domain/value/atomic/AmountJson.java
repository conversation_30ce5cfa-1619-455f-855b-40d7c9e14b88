package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.io.IOException;
import java.math.BigInteger;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class AmountJson {

    static class Serializer extends JsonSerializer<Amount> {

        @Override
        public void serialize(Amount value, JsonGenerator generator, SerializerProvider provider) throws IOException {
            if (value == null) {
                return;
            }

            generator.writeNumber(value.getValue().longValue());
        }
    }

    static class Deserializer extends JsonDeserializer<Amount> {

        @Override
        public Amount deserialize(<PERSON><PERSON><PERSON><PERSON><PERSON> parser, DeserializationContext context) throws IOException {
            String value = parser.getText();
            return Amount.of(new BigInteger(value));
        }
    }
}
