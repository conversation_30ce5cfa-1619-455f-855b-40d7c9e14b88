package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class ZoneIdJson {

    static class Serializer extends JsonSerializer<ZoneId> {

        @Override
        public void serialize(ZoneId value, JsonGenerator generator, SerializerProvider provider) throws IOException {
            if (value == null) {
                return;
            }

            generator.writeNumber(value.getValue().longValue());
        }
    }

    static class Deserializer extends JsonDeserializer<ZoneId> {

        @Override
        public ZoneId deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            Integer value = Integer.valueOf(parser.getIntValue());
            return ZoneId.of(value);
        }
    }
}
