package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * QRサインイン状態を表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum QrStatus {

    /**
     * pending :
     *  - qr_flow_type = 'sign_in' の場合 認証アプリ認証前。
     *  - qr_flow_type = 'confirmation' の場合 手続き待ち。
     */
    PENDING("pending"),

    /**
     * in_processing :
     *  - qr_flow_type = 'confirmation' の場合 処理中。
     */
    IN_PROCESSING("in_processing"),

    /**
     * authenticated :
     *  - qr_flow_type = 'sign_in' の場合 認証済み。
     */
    AUTHENTICATED("authenticated", true),

    /**
     * completed :
     *  - qr_flow_type = 'sign_in' の場合 トークン発行済。
     *  - qr_flow_type = 'confirmation' の場合 処理済み。
     */
    COMPLETED("completed", true),

    /**
     * failure :
     *  - qr_flow_type = 'confirmation' の場合 処理失敗。
     */
    FAILURE("failure", true);

    private static final Map<String, QrStatus> OBJECT_MAP = Arrays.stream(QrStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final boolean processed;

    private QrStatus(String value) {
        this(value, false);
    }

    private QrStatus(String value, boolean processed) {
        this.value = value;
        this.processed = processed;
    }

    public String getValue() {
        return this.value;
    }

    public boolean isProcessed() {
        return this.processed;
    }

    public static QrStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
