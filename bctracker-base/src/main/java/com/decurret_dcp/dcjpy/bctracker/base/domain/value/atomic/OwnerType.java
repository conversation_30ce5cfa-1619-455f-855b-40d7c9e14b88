package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 利用元種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum OwnerType {

    /** user : 個人ユーザ/法人ユーザ。 */
    USER("user"),

    /** service : 銀行/事業者。 */
    SERVICE("service");

    private static final Map<String, OwnerType> OBJECT_MAP = Arrays.stream(OwnerType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private OwnerType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static OwnerType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
