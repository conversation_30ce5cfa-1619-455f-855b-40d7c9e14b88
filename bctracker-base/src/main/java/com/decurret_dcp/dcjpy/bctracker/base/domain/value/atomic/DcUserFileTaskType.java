package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 個人ユーザ/法人ユーザのファイルタスクを表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum DcUserFileTaskType {

    /** list_nft : NFT一覧ファイル作成。 */
    LIST_NFT("list_nft");

    private static final Map<String, DcUserFileTaskType> OBJECT_MAP
            = Arrays.stream(DcUserFileTaskType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private DcUserFileTaskType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static DcUserFileTaskType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
