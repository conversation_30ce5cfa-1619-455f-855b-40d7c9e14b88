package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class TraceId {

    private final String value;

    private TraceId(String value) {
        this.value = value;
    }

    public static TraceId of(String traceId) {
        return new TraceId(traceId);
    }

    public String getValue() {
        return this.value;
    }
}
