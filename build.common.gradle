group = 'com.decurret_dcp'
version = '1.0-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    // basic
    implementation 'org.springframework.boot:spring-boot-starter'

    // spring-starter-web なしで jackson を利用するための依存関係
    implementation 'org.springframework.boot:spring-boot-starter-json'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // actuator
    implementation 'org.springframework.boot:spring-boot-starter-actuator'


    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // postgresql/doma
    runtimeOnly 'org.postgresql:postgresql'
    implementation 'org.seasar.doma.boot:doma-spring-boot-starter:1.7.0'
    annotationProcessor "org.seasar.doma:doma-processor:2.53.1"

    // aws
    implementation platform('software.amazon.awssdk:bom:2.17.10')
    implementation 'software.amazon.awssdk:sqs'
    implementation 'software.amazon.awssdk:sts'
    implementation 'software.amazon.awssdk:secretsmanager'

    // ランダム文字列生成に利用
    implementation 'org.apache.commons:commons-text:1.11.0'
    // キャメルケースの文字列をスネークケースに変換するために利用している
    implementation 'com.google.guava:guava:31.1-jre'
    // Http通信
    compileOnly 'org.apache.httpcomponents:httpclient:4.5.13'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // spock
    testImplementation 'org.codehaus.groovy:groovy:3.0.10'
    testImplementation platform("org.spockframework:spock-bom:2.1-groovy-3.0")
    testImplementation 'org.spockframework:spock-core:2.1-groovy-3.0'
    testImplementation 'org.spockframework:spock-spring:2.1-groovy-3.0'
    testImplementation 'com.athaydes:spock-reports:2.3.0-groovy-3.0'

    //adhoc
    testImplementation 'org.codehaus.groovy:groovy-sql:3.0.14'
    testImplementation 'org.testcontainers:testcontainers:1.17.6'
    testImplementation 'org.testcontainers:spock:1.17.6'
    testImplementation 'org.testcontainers:postgresql:1.17.6'
    testImplementation 'org.apache.httpcomponents:httpclient:4.5.13'
    testImplementation "com.github.tomakehurst:wiremock-jre8:2.35.0"
}

tasks.compileJava {
    options.compilerArgs = [
            '-Adoma.dao.subpackage=impl',
            '-Adoma.dao.suffix=Impl'
    ]
}

tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        events "STARTED", "PASSED", "FAILED", "SKIPPED"
        displayGranularity = 4
    }
}

jacoco {
    toolVersion = "0.8.7"
}

jacocoTestReport {
    reports {
        xml.required = false
        csv.required = false
        html.outputLocation = layout.buildDirectory.dir("jacocoHtml")
    }
}
test.finalizedBy jacocoTestReport

eclipse {
    classpath {
        file {
            whenMerged { classpath ->
                classpath.entries.removeAll { it.path == ".apt_generated" }
            }
            withXml { provider ->
                def node = provider.asNode()
                // specify output path for .apt_generated
                node.appendNode("classpathentry", [kind: "src", output: "bin/main", path: ".apt_generated"])
            }
        }

        downloadJavadoc = true
        downloadSources = true
    }
}

idea {
    module {
        downloadJavadoc = true
        downloadSources = true
    }
}
